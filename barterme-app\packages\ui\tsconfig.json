{"compilerOptions": {"target": "ES2020", "module": "ESNext", "lib": ["es2020", "DOM"], "jsx": "react-native", "declaration": true, "strict": true, "moduleResolution": "node", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "baseUrl": ".", "paths": {"@components/*": ["src/components/*"], "@hooks/*": ["src/hooks/*"], "@theme/*": ["src/theme/*"], "@utils/*": ["src/utils/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts", "**/*.test.tsx"]}