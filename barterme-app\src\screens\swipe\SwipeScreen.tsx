import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  Dimensions,
  Animated,
  PanResponder,
  Alert,
  Modal,
  ScrollView,
  Switch,
  TextInput,
  StatusBar,
  SafeAreaView,
  Platform,
} from 'react-native';
import Slider from '@react-native-community/slider';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../../theme/ThemeProvider';
import { Ionicons } from '@expo/vector-icons';

// Mock data for items to swipe through
const ITEMS = [
  {
    id: 1,
    title: 'iPhone 12',
    description: 'Good condition, 1 year old',
    image: 'https://picsum.photos/id/1/400/600',
    owner: 'Jessica',
    distance: '2.5 miles away',
    condition: 'Good',
    category: 'Electronics',
    value: '$500',
  },
  {
    id: 2,
    title: 'MacBook Air 2020',
    description: 'Barely used, comes with charger',
    image: 'https://picsum.photos/id/2/400/600',
    owner: '<PERSON>',
    distance: '3 miles away',
    condition: 'Excellent',
    category: 'Electronics',
    value: '$900',
  },
  {
    id: 3,
    title: 'Sony PlayStation 5',
    description: 'Brand new in box, never opened',
    image: 'https://picsum.photos/id/3/400/600',
    owner: 'David',
    distance: '1 mile away',
    condition: 'New',
    category: 'Gaming',
    value: '$550',
  },
  {
    id: 4,
    title: 'Apple Watch Series 6',
    description: 'Great condition, minor scratches',
    image: 'https://picsum.photos/id/4/400/600',
    owner: 'Sarah',
    distance: '4.2 miles away',
    condition: 'Good',
    category: 'Wearables',
    value: '$250',
  },
];

// Mock user items to trade with
const MY_ITEMS = [
  {
    id: 'my1',
    title: 'Nintendo Switch',
    image: 'https://picsum.photos/id/10/200/200',
    value: '$250',
  },
  {
    id: 'my2',
    title: 'iPad Pro 2021',
    image: 'https://picsum.photos/id/11/200/200',
    value: '$750',
  },
  {
    id: 'my3',
    title: 'Sony Headphones',
    image: 'https://picsum.photos/id/12/200/200',
    value: '$150',
  },
];

function SwipeScreen() {
  const navigation = useNavigation<any>();
  const { isDark, colors } = useTheme();
  const [currentIndex, setCurrentIndex] = useState(0);
  const position = useRef(new Animated.ValueXY()).current;
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [myItemModalVisible, setMyItemModalVisible] = useState(false);
  const [maxDistance, setMaxDistance] = useState(10);
  const [categories, setCategories] = useState({
    Electronics: true,
    Gaming: true,
    Wearables: true,
    Clothing: false,
    Books: false,
    Home: false,
  });
  const [selectedConditions, setSelectedConditions] = useState({
    New: true,
    Excellent: true,
    Good: true,
    Fair: true,
    Poor: false,
  });
  const [minValue, setMinValue] = useState(0);
  const [maxValue, setMaxValue] = useState(1000);
  const [selectedMyItem, setSelectedMyItem] = useState(MY_ITEMS[0]);

  // Configure the PanResponder for swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onPanResponderMove: (_, gesture) => {
        position.setValue({ x: gesture.dx, y: gesture.dy });
      },
      onPanResponderRelease: (_, gesture) => {
        if (gesture.dx > 120) {
          // Swipe right (like)
          swipeRight();
        } else if (gesture.dx < -120) {
          // Swipe left (dislike)
          swipeLeft();
        } else {
          // Reset position if not swiped far enough
          Animated.spring(position, {
            toValue: { x: 0, y: 0 },
            friction: 5,
            useNativeDriver: false,
          }).start();
        }
      },
    })
  ).current;

  // Handle swipe right (like)
  const swipeRight = () => {
    Animated.timing(position, {
      toValue: { x: 500, y: 0 },
      duration: 300,
      useNativeDriver: false,
    }).start(() => {
      // You could trigger a match here
      nextCard();
    });
  };

  // Handle swipe left (dislike)
  const swipeLeft = () => {
    Animated.timing(position, {
      toValue: { x: -500, y: 0 },
      duration: 300,
      useNativeDriver: false,
    }).start(nextCard);
  };

  // Move to the next card
  const nextCard = () => {
    setCurrentIndex((prevIndex) => {
      const nextIndex = prevIndex + 1;
      if (nextIndex >= ITEMS.length) {
        // If we've reached the end, show a message or reset
        Alert.alert("That's all for now!", 'Check back later for more items');
        return prevIndex;
      }
      position.setValue({ x: 0, y: 0 });
      return nextIndex;
    });
  };

  // Handle filter button press
  const handleFilterPress = () => {
    setFilterModalVisible(true);
  };

  // Handle my item badge press
  const handleMyItemPress = () => {
    setMyItemModalVisible(true);
  };

  // Handle matches button press
  const handleMatchesPress = () => {
    navigation.navigate('TradeDetails', { tradeId: 'matches' });
  };

  // Handle pressing the item to see details
  const handleViewDetails = () => {
    navigation.navigate('ItemDetails', { itemId: ITEMS[currentIndex].id });
  };

  // Rotation for the card when swiping
  const rotate = position.x.interpolate({
    inputRange: [-300, 0, 300],
    outputRange: ['-15deg', '0deg', '15deg'],
    extrapolate: 'clamp',
  });

  // Opacity for like/dislike indicators
  const likeOpacity = position.x.interpolate({
    inputRange: [0, 150],
    outputRange: [0, 1],
    extrapolate: 'clamp',
  });

  const dislikeOpacity = position.x.interpolate({
    inputRange: [-150, 0],
    outputRange: [1, 0],
    extrapolate: 'clamp',
  });

  // "Yeah!" text opacity
  const yeahOpacity = position.x.interpolate({
    inputRange: [0, 100, 200],
    outputRange: [0, 0.5, 1],
    extrapolate: 'clamp',
  });

  // "Nah!" text opacity
  const nahOpacity = position.x.interpolate({
    inputRange: [-200, -100, 0],
    outputRange: [1, 0.5, 0],
    extrapolate: 'clamp',
  });

  // "Like" and "Dislike" button handlers
  const handleLike = () => {
    swipeRight();
  };

  const handleDislike = () => {
    swipeLeft();
  };

  // Render the current card
  const renderCard = () => {
    if (currentIndex >= ITEMS.length) {
      return (
        <View
          style={[
            styles.emptyState,
            { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
          ]}
        >
          <Text
            style={[
              styles.emptyStateText,
              { color: isDark ? '#FFFFFF' : '#000000' },
            ]}
          >
            No more items to show
          </Text>
        </View>
      );
    }

    const item = ITEMS[currentIndex];

    return (
      <Animated.View
        {...panResponder.panHandlers}
        style={[
          styles.card,
          {
            backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF',
            transform: [
              { translateX: position.x },
              { translateY: position.y },
              { rotate: rotate },
            ],
          },
        ]}
      >
        <TouchableOpacity
          activeOpacity={0.9}
          onPress={handleViewDetails}
          style={{ flex: 1 }}
        >
          <Image source={{ uri: item.image }} style={styles.image} />
          <View style={styles.infoContainer}>
            <Text
              style={[styles.title, { color: isDark ? '#FFFFFF' : '#000000' }]}
            >
              {item.title}
            </Text>
            <Text
              style={[
                styles.description,
                { color: isDark ? '#BBBBBB' : '#757575' },
              ]}
            >
              {item.description}
            </Text>

            <View style={styles.itemDetails}>
              <Text
                style={[
                  styles.detailText,
                  { color: isDark ? '#BBBBBB' : '#757575' },
                ]}
              >
                Condition: {item.condition}
              </Text>
              <Text
                style={[
                  styles.detailText,
                  { color: isDark ? '#BBBBBB' : '#757575' },
                ]}
              >
                Value: {item.value}
              </Text>
            </View>

            <View style={styles.ownerContainer}>
              <Text
                style={[
                  styles.ownerText,
                  { color: isDark ? '#BBBBBB' : '#757575' },
                ]}
              >
                {item.owner} • {item.distance}
              </Text>
            </View>
          </View>

          {/* Like indicator overlay */}
          <Animated.View
            style={[styles.likeContainer, { opacity: likeOpacity }]}
          >
            <Text style={styles.likeText}>LIKE</Text>
          </Animated.View>

          {/* Dislike indicator overlay */}
          <Animated.View
            style={[styles.dislikeContainer, { opacity: dislikeOpacity }]}
          >
            <Text style={styles.dislikeText}>NOPE</Text>
          </Animated.View>

          {/* Yeah! overlay */}
          <Animated.View
            style={[styles.yeahContainer, { opacity: yeahOpacity }]}
            pointerEvents="none"
          >
            <Text style={styles.yeahText}>Yeah!</Text>
          </Animated.View>

          {/* Nah! overlay */}
          <Animated.View
            style={[styles.nahContainer, { opacity: nahOpacity }]}
            pointerEvents="none"
          >
            <Text style={styles.nahText}>Nah!</Text>
          </Animated.View>
        </TouchableOpacity>
      </Animated.View>
    );
  };

  // Render the Filter Modal
  const renderFilterModal = () => {
    return (
      <Modal
        visible={filterModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setFilterModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDark ? '#FFFFFF' : '#000000' },
                ]}
              >
                Filters
              </Text>
              <TouchableOpacity onPress={() => setFilterModalVisible(false)}>
                <Ionicons
                  name="close"
                  size={24}
                  color={isDark ? '#FFFFFF' : '#000000'}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScroll}>
              {/* Distance Filter */}
              <View style={styles.filterSection}>
                <Text
                  style={[
                    styles.filterTitle,
                    { color: isDark ? '#FFFFFF' : '#000000' },
                  ]}
                >
                  Maximum Distance
                </Text>
                <Text
                  style={[
                    styles.filterValue,
                    { color: isDark ? '#BBBBBB' : '#757575' },
                  ]}
                >
                  {maxDistance} miles
                </Text>
                <Slider
                  style={styles.slider}
                  minimumValue={1}
                  maximumValue={50}
                  step={1}
                  value={maxDistance}
                  onValueChange={setMaxDistance}
                  minimumTrackTintColor={colors.primary}
                  maximumTrackTintColor={isDark ? '#555555' : '#DDDDDD'}
                />
              </View>

              {/* Price Range Filter */}
              <View style={styles.filterSection}>
                <Text
                  style={[
                    styles.filterTitle,
                    { color: isDark ? '#FFFFFF' : '#000000' },
                  ]}
                >
                  Price Range
                </Text>
                <Text
                  style={[
                    styles.filterValue,
                    { color: isDark ? '#BBBBBB' : '#757575' },
                  ]}
                >
                  ${minValue} - ${maxValue}
                </Text>
                <View style={styles.priceInputs}>
                  <TextInput
                    style={[
                      styles.priceInput,
                      {
                        backgroundColor: isDark ? '#333333' : '#F5F5F5',
                        color: isDark ? '#FFFFFF' : '#000000',
                      },
                    ]}
                    value={minValue.toString()}
                    onChangeText={(text) => setMinValue(parseInt(text) || 0)}
                    keyboardType="numeric"
                    placeholder="Min"
                    placeholderTextColor={isDark ? '#888888' : '#AAAAAA'}
                  />
                  <Text
                    style={{
                      color: isDark ? '#FFFFFF' : '#000000',
                      marginHorizontal: 10,
                    }}
                  >
                    to
                  </Text>
                  <TextInput
                    style={[
                      styles.priceInput,
                      {
                        backgroundColor: isDark ? '#333333' : '#F5F5F5',
                        color: isDark ? '#FFFFFF' : '#000000',
                      },
                    ]}
                    value={maxValue.toString()}
                    onChangeText={(text) => setMaxValue(parseInt(text) || 0)}
                    keyboardType="numeric"
                    placeholder="Max"
                    placeholderTextColor={isDark ? '#888888' : '#AAAAAA'}
                  />
                </View>
              </View>

              {/* Categories Filter */}
              <View style={styles.filterSection}>
                <Text
                  style={[
                    styles.filterTitle,
                    { color: isDark ? '#FFFFFF' : '#000000' },
                  ]}
                >
                  Categories
                </Text>
                {Object.keys(categories).map((category) => (
                  <View key={category} style={styles.checkboxRow}>
                    <Text
                      style={[
                        styles.checkboxLabel,
                        { color: isDark ? '#FFFFFF' : '#000000' },
                      ]}
                    >
                      {category}
                    </Text>
                    <Switch
                      value={categories[category]}
                      onValueChange={(value) =>
                        setCategories({ ...categories, [category]: value })
                      }
                      trackColor={{
                        false: '#767577',
                        true: colors.primary + '80',
                      }}
                      thumbColor={
                        categories[category] ? colors.primary : '#f4f3f4'
                      }
                    />
                  </View>
                ))}
              </View>

              {/* Condition Filter */}
              <View style={styles.filterSection}>
                <Text
                  style={[
                    styles.filterTitle,
                    { color: isDark ? '#FFFFFF' : '#000000' },
                  ]}
                >
                  Condition
                </Text>
                {Object.keys(selectedConditions).map((condition) => (
                  <View key={condition} style={styles.checkboxRow}>
                    <Text
                      style={[
                        styles.checkboxLabel,
                        { color: isDark ? '#FFFFFF' : '#000000' },
                      ]}
                    >
                      {condition}
                    </Text>
                    <Switch
                      value={selectedConditions[condition]}
                      onValueChange={(value) =>
                        setSelectedConditions({
                          ...selectedConditions,
                          [condition]: value,
                        })
                      }
                      trackColor={{
                        false: '#767577',
                        true: colors.primary + '80',
                      }}
                      thumbColor={
                        selectedConditions[condition]
                          ? colors.primary
                          : '#f4f3f4'
                      }
                    />
                  </View>
                ))}
              </View>
            </ScrollView>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={[
                  styles.resetButton,
                  { borderColor: isDark ? '#FFFFFF' : '#000000' },
                ]}
                onPress={() => {
                  setMaxDistance(10);
                  setMinValue(0);
                  setMaxValue(1000);
                  setCategories({
                    Electronics: true,
                    Gaming: true,
                    Wearables: true,
                    Clothing: false,
                    Books: false,
                    Home: false,
                  });
                  setSelectedConditions({
                    New: true,
                    Excellent: true,
                    Good: true,
                    Fair: true,
                    Poor: false,
                  });
                }}
              >
                <Text style={{ color: isDark ? '#FFFFFF' : '#000000' }}>
                  Reset
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.applyButton,
                  { backgroundColor: colors.primary },
                ]}
                onPress={() => setFilterModalVisible(false)}
              >
                <Text style={{ color: '#FFFFFF', fontWeight: 'bold' }}>
                  Apply Filters
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  // Render the My Item Selection Modal
  const renderMyItemModal = () => {
    return (
      <Modal
        visible={myItemModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setMyItemModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View
            style={[
              styles.modalContent,
              { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
            ]}
          >
            <View style={styles.modalHeader}>
              <Text
                style={[
                  styles.modalTitle,
                  { color: isDark ? '#FFFFFF' : '#000000' },
                ]}
              >
                Select Your Item
              </Text>
              <TouchableOpacity onPress={() => setMyItemModalVisible(false)}>
                <Ionicons
                  name="close"
                  size={24}
                  color={isDark ? '#FFFFFF' : '#000000'}
                />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.modalScroll}>
              {MY_ITEMS.map((item) => (
                <TouchableOpacity
                  key={item.id}
                  style={[
                    styles.myItemRow,
                    {
                      backgroundColor:
                        selectedMyItem.id === item.id
                          ? isDark
                            ? '#333333'
                            : '#F0F0F0'
                          : 'transparent',
                    },
                  ]}
                  onPress={() => {
                    setSelectedMyItem(item);
                    setMyItemModalVisible(false);
                  }}
                >
                  <Image
                    source={{ uri: item.image }}
                    style={styles.myItemImage}
                  />
                  <View style={styles.myItemInfo}>
                    <Text
                      style={[
                        styles.myItemTitle,
                        { color: isDark ? '#FFFFFF' : '#000000' },
                      ]}
                    >
                      {item.title}
                    </Text>
                    <Text
                      style={[
                        styles.myItemValue,
                        { color: isDark ? '#BBBBBB' : '#757575' },
                      ]}
                    >
                      {item.value}
                    </Text>
                  </View>
                  {selectedMyItem.id === item.id && (
                    <Ionicons
                      name="checkmark-circle"
                      size={24}
                      color={colors.primary}
                    />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>

            <TouchableOpacity
              style={[
                styles.addItemButton,
                { backgroundColor: colors.primary },
              ]}
              onPress={() => {
                setMyItemModalVisible(false);
                navigation.navigate('Profile');
              }}
            >
              <Ionicons name="add" size={20} color="#FFFFFF" />
              <Text
                style={{ color: '#FFFFFF', fontWeight: 'bold', marginLeft: 8 }}
              >
                Add New Item
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <SafeAreaView
      style={[
        styles.safeContainer,
        { backgroundColor: isDark ? '#121212' : '#F5F5F5' },
      ]}
    >
      <StatusBar
        barStyle={isDark ? 'light-content' : 'dark-content'}
        backgroundColor={isDark ? '#121212' : '#F5F5F5'}
      />
      <View
        style={[
          styles.container,
          { backgroundColor: isDark ? '#121212' : '#F5F5F5' },
        ]}
      >
        {/* Header */}
        <View style={styles.header}>
          {/* Filter Button */}
          <TouchableOpacity
            style={styles.headerButton}
            onPress={handleFilterPress}
          >
            <Ionicons
              name="options-outline"
              size={24}
              color={isDark ? '#FFFFFF' : '#000000'}
            />
          </TouchableOpacity>

          {/* Matches Button */}
          <TouchableOpacity
            style={styles.matchesButton}
            onPress={handleMatchesPress}
          >
            <Ionicons
              name="people-outline"
              size={24}
              color={isDark ? '#FFFFFF' : '#000000'}
            />
            <Text
              style={[
                styles.matchesText,
                { color: isDark ? '#FFFFFF' : '#000000' },
              ]}
            >
              Matches
            </Text>
          </TouchableOpacity>

          {/* My Trading Item Badge */}
          <TouchableOpacity
            style={styles.myItemBadge}
            onPress={handleMyItemPress}
          >
            <Image
              source={{ uri: selectedMyItem.image }}
              style={styles.myItemBadgeImage}
            />
          </TouchableOpacity>
        </View>

        <View style={styles.cardContainer}>{renderCard()}</View>

        {/* Bottom swipe buttons */}
        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={[styles.circleButton, styles.dislikeButton]}
            onPress={handleDislike}
          >
            <Ionicons name="close" size={30} color="white" />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.circleButton, styles.likeButton]}
            onPress={handleLike}
          >
            <Ionicons name="checkmark" size={30} color="white" />
          </TouchableOpacity>
        </View>

        {/* Render Modals */}
        {renderFilterModal()}
        {renderMyItemModal()}
      </View>
    </SafeAreaView>
  );
}

const { width, height } = Dimensions.get('window');

const styles = StyleSheet.create({
  safeContainer: {
    flex: 1,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  container: {
    flex: 1,
  },
  header: {
    height: 60,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginTop: 10,
    marginBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E020',
  },
  headerButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
  },
  matchesButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  matchesText: {
    marginLeft: 4,
    fontSize: 14,
    fontWeight: '500',
  },
  myItemBadge: {
    width: 44,
    height: 44,
    borderRadius: 22,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#FF6B6B',
    overflow: 'hidden',
  },
  myItemBadgeImage: {
    width: 40,
    height: 40,
    borderRadius: 20,
  },
  cardContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 10,
  },
  card: {
    width: width * 0.9,
    height: height * 0.6,
    borderRadius: 10,
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
    elevation: 3,
    position: 'absolute',
  },
  image: {
    width: '100%',
    height: '65%',
    borderTopLeftRadius: 10,
    borderTopRightRadius: 10,
  },
  infoContainer: {
    padding: 15,
  },
  title: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  description: {
    fontSize: 16,
    marginBottom: 10,
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginVertical: 5,
  },
  detailText: {
    fontSize: 14,
  },
  ownerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  ownerText: {
    fontSize: 14,
  },
  likeContainer: {
    position: 'absolute',
    top: 50,
    right: 40,
    transform: [{ rotate: '15deg' }],
    borderWidth: 4,
    borderRadius: 5,
    borderColor: '#4CAF50',
    padding: 10,
    backgroundColor: 'rgba(76, 175, 80, 0.1)',
  },
  dislikeContainer: {
    position: 'absolute',
    top: 50,
    left: 40,
    transform: [{ rotate: '-15deg' }],
    borderWidth: 4,
    borderRadius: 5,
    borderColor: '#FF6B6B',
    padding: 10,
    backgroundColor: 'rgba(255, 107, 107, 0.1)',
  },
  likeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  dislikeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  yeahContainer: {
    position: 'absolute',
    top: 50,
    right: 40,
    transform: [{ rotate: '15deg' }],
    borderWidth: 4,
    borderRadius: 5,
    borderColor: '#4CAF50',
    padding: 10,
    backgroundColor: 'rgba(76, 175, 80, 0.3)',
  },
  nahContainer: {
    position: 'absolute',
    top: 50,
    left: 40,
    transform: [{ rotate: '-15deg' }],
    borderWidth: 4,
    borderRadius: 5,
    borderColor: '#FF6B6B',
    padding: 10,
    backgroundColor: 'rgba(255, 107, 107, 0.3)',
  },
  yeahText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  nahText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FF6B6B',
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 20,
    paddingTop: 10,
  },
  circleButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
    marginHorizontal: 15,
  },
  likeButton: {
    backgroundColor: '#4CAF50',
  },
  dislikeButton: {
    backgroundColor: '#FF6B6B',
  },
  emptyState: {
    width: width * 0.9,
    height: height * 0.5,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
  },
  emptyStateText: {
    fontSize: 18,
    fontWeight: '500',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  modalScroll: {
    marginBottom: 20,
  },
  filterSection: {
    marginBottom: 20,
  },
  filterTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
  },
  filterValue: {
    fontSize: 14,
    marginBottom: 5,
  },
  slider: {
    width: '100%',
    height: 40,
  },
  checkboxRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  checkboxLabel: {
    fontSize: 16,
  },
  priceInputs: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  priceInput: {
    flex: 1,
    padding: 10,
    borderRadius: 8,
  },
  modalFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  resetButton: {
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 10,
  },
  applyButton: {
    flex: 2,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  myItemRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 10,
    marginBottom: 10,
  },
  myItemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  myItemInfo: {
    flex: 1,
    marginLeft: 15,
  },
  myItemTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  myItemValue: {
    fontSize: 14,
  },
  addItemButton: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 15,
    borderRadius: 8,
    marginTop: 10,
  },
});

export default SwipeScreen;
