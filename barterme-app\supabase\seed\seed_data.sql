-- Sample seed data for development purposes

-- Sample Users
INSERT INTO users (id, email, username, display_name, avatar_url, bio, location, reputation_score, is_verified)
VALUES
  ('a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '<EMAIL>', 'trader_joe', '<PERSON>', 'https://randomuser.me/api/portraits/men/1.jpg', 'Vintage collector and trading enthusiast', 'New York, NY', 4.8, true),
  ('b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', '<EMAIL>', 'gadget_guru', '<PERSON>', 'https://randomuser.me/api/portraits/women/2.jpg', 'Tech enthusiast looking to trade gadgets', 'San Francisco, CA', 4.5, true),
  ('c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '<EMAIL>', 'vintage_vibes', '<PERSON>', 'https://randomuser.me/api/portraits/men/3.jpg', 'Collecting and trading vintage items', 'Chicago, IL', 4.2, false),
  ('d0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', '<EMAIL>', 'fashion_swap', 'Emma Wilson', 'https://randomuser.me/api/portraits/women/4.jpg', 'Fashion lover always looking for new styles', 'Miami, FL', 4.9, true),
  ('e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', '<EMAIL>', 'book_trader', 'David Lee', 'https://randomuser.me/api/portraits/men/5.jpg', 'Book collector and trading enthusiast', 'Boston, MA', 4.6, false);

-- Sample Items
INSERT INTO items (id, user_id, title, description, condition, category, subcategory, estimated_value, is_available, is_featured)
VALUES
  ('a1eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'Vintage Leather Jacket', 'Genuine leather jacket from the 80s, excellent condition', 'Good', 'Clothing', 'Outerwear', 150.00, true, true),
  ('b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'Vinyl Record Collection', 'Collection of 25 classic rock vinyl records', 'Excellent', 'Music', 'Vinyl', 200.00, true, false),
  ('c1eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'iPhone 12 Pro', 'iPhone 12 Pro 128GB, minor scratches on screen', 'Good', 'Electronics', 'Phones', 400.00, true, true),
  ('d1eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'Gaming Laptop', 'ASUS ROG Gaming Laptop, 2 years old but works great', 'Fair', 'Electronics', 'Computers', 600.00, true, false),
  ('e1eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'Antique Pocket Watch', 'Gold-plated pocket watch from 1920s', 'Excellent', 'Collectibles', 'Watches', 300.00, true, true),
  ('f1eebc99-9c0b-4ef8-bb6d-6bb9bd380a16', 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'Vintage Camera', 'Polaroid SX-70 camera, fully functional', 'Good', 'Electronics', 'Cameras', 150.00, true, false),
  ('g1eebc99-9c0b-4ef8-bb6d-6bb9bd380a17', 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'Designer Handbag', 'Authentic Gucci handbag, barely used', 'Like New', 'Fashion', 'Bags', 800.00, true, true),
  ('h1eebc99-9c0b-4ef8-bb6d-6bb9bd380a18', 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'Luxury Watch', 'Tag Heuer watch, working perfectly', 'Excellent', 'Fashion', 'Watches', 1200.00, true, false),
  ('i1eebc99-9c0b-4ef8-bb6d-6bb9bd380a19', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'Rare Book Collection', 'First edition set of Lord of the Rings', 'Good', 'Books', 'Fiction', 350.00, true, true),
  ('j1eebc99-9c0b-4ef8-bb6d-6bb9bd380a20', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'Vintage Comic Books', 'Collection of Marvel comics from the 90s', 'Fair', 'Books', 'Comics', 200.00, true, false);

-- Sample Item Images
INSERT INTO item_images (item_id, storage_path, display_order)
VALUES
  ('a1eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'items/leather_jacket_1.jpg', 0),
  ('a1eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'items/leather_jacket_2.jpg', 1),
  ('b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'items/vinyl_collection_1.jpg', 0),
  ('c1eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'items/iphone_1.jpg', 0),
  ('c1eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'items/iphone_2.jpg', 1),
  ('d1eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'items/laptop_1.jpg', 0),
  ('e1eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'items/pocket_watch_1.jpg', 0),
  ('f1eebc99-9c0b-4ef8-bb6d-6bb9bd380a16', 'items/camera_1.jpg', 0),
  ('g1eebc99-9c0b-4ef8-bb6d-6bb9bd380a17', 'items/handbag_1.jpg', 0),
  ('h1eebc99-9c0b-4ef8-bb6d-6bb9bd380a18', 'items/watch_1.jpg', 0),
  ('i1eebc99-9c0b-4ef8-bb6d-6bb9bd380a19', 'items/books_1.jpg', 0),
  ('j1eebc99-9c0b-4ef8-bb6d-6bb9bd380a20', 'items/comics_1.jpg', 0);

-- Sample Trades
INSERT INTO trades (id, initiator_id, responder_id, status, is_public, cash_amount, cash_direction)
VALUES
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'pending', true, 0, null),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'accepted', true, 50, 'initiator_to_responder'),
  ('c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'completed', false, 0, null),
  ('d2eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'rejected', true, 0, null);

-- Sample Trade Items
INSERT INTO trade_items (trade_id, item_id, offered_by, is_confirmed)
VALUES
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'a1eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', false),
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'c1eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', false),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'e1eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', true),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'g1eebc99-9c0b-4ef8-bb6d-6bb9bd380a17', 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', true),
  ('c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'i1eebc99-9c0b-4ef8-bb6d-6bb9bd380a19', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', true),
  ('c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'b1eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', true),
  ('d2eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'd1eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', false),
  ('d2eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'j1eebc99-9c0b-4ef8-bb6d-6bb9bd380a20', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', false);

-- Sample Messages
INSERT INTO messages (trade_id, sender_id, content, is_read)
VALUES
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'Hey, I really like your iPhone. Would you trade it for my leather jacket?', true),
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'Your jacket looks nice but I''m looking for something of higher value for my iPhone.', true),
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'I could add my vinyl collection to sweeten the deal?', false),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'Would you trade your handbag for my pocket watch plus $50?', true),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', 'That sounds like a fair trade. I accept!', true),
  ('c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'Would you trade your vinyl collection for my rare book collection?', true),
  ('c2eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'Yes, that sounds good to me. Let''s make the trade!', true);

-- Sample Trade Reactions
INSERT INTO trade_reactions (trade_id, user_id, reaction)
VALUES
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13', '🔥'),
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14', '👍'),
  ('a2eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', '😂'),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', '🔥'),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', '🤯');

-- Sample Trade Votes
INSERT INTO trade_votes (trade_id, voter_id, winner_id)
VALUES
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11', 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14'),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'b0eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'c0eebc99-9c0b-4ef8-bb6d-6bb9bd380a13'),
  ('b2eebc99-9c0b-4ef8-bb6d-6bb9bd380a12', 'e0eebc99-9c0b-4ef8-bb6d-6bb9bd380a15', 'd0eebc99-9c0b-4ef8-bb6d-6bb9bd380a14'); 