import React, { useState } from 'react';
import {
  View,
  Text,
  Image,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type ItemDetailsScreenRouteProp = RouteProp<RootStackParamList, 'ItemDetails'>;

type ItemDetailsScreenProps = {
  route: ItemDetailsScreenRouteProp;
  navigation: NativeStackNavigationProp<RootStackParamList, 'ItemDetails'>;
};

type ItemData = {
  id: string;
  title: string;
  price: string;
  description: string;
  condition: string;
  category: string;
  owner: {
    id?: string;
    name: string;
    avatar: string;
    rating: number;
  };
  images: string[];
};

const ItemDetailsScreen: React.FC<ItemDetailsScreenProps> = ({
  route,
  navigation,
}) => {
  const { itemId } = route.params;
  const [isLiked, setIsLiked] = useState<boolean>(false);
  const { colors } = useTheme();

  // Placeholder item data
  const item: ItemData = {
    id: itemId,
    title: 'Vintage Leather Jacket',
    price: '$120',
    description:
      'A genuine leather jacket in excellent condition. Worn only a few times. Perfect for fall weather. Includes original tags and packaging.',
    condition: 'Excellent',
    category: 'Clothing',
    owner: {
      id: 'user123',
      name: 'Alex Johnson',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      rating: 4.8,
    },
    images: [
      'https://images.unsplash.com/photo-1551028719-00167b16eac5?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    ],
  };

  const handleBack = (): void => {
    navigation.goBack();
  };

  const handleTradeRequest = (): void => {
    navigation.navigate('TradeOffer', { itemId });
  };

  const handleMessage = (): void => {
    navigation.navigate('Chat', { userId: item.owner.id });
  };

  const toggleLike = (): void => {
    setIsLiked(!isLiked);
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={handleBack} style={styles.backButton}>
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <TouchableOpacity onPress={toggleLike} style={styles.likeButton}>
          <Ionicons
            name={isLiked ? 'heart' : 'heart-outline'}
            size={24}
            color={isLiked ? colors.primary : colors.text}
          />
        </TouchableOpacity>
      </View>

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 100 }}
      >
        {/* Image Container */}
        <View style={styles.imageContainer}>
          <Image
            source={{ uri: item.images[0] }}
            style={styles.image}
            resizeMode="cover"
          />
        </View>

        {/* Item Info */}
        <View
          style={[styles.infoContainer, { borderBottomColor: colors.border }]}
        >
          <Text style={[styles.title, { color: colors.text }]}>
            {item.title}
          </Text>
          <Text style={[styles.price, { color: colors.primary }]}>
            {item.price}
          </Text>
          <View style={styles.infoRow}>
            <Text style={[styles.label, { color: colors.text + '80' }]}>
              Condition:
            </Text>
            <Text style={[styles.value, { color: colors.text }]}>
              {item.condition}
            </Text>
          </View>
          <View style={styles.infoRow}>
            <Text style={[styles.label, { color: colors.text + '80' }]}>
              Category:
            </Text>
            <Text style={[styles.value, { color: colors.text }]}>
              {item.category}
            </Text>
          </View>
        </View>

        {/* Owner Info */}
        <View
          style={[styles.ownerContainer, { borderBottomColor: colors.border }]}
        >
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Owner
          </Text>
          <View style={styles.ownerInfo}>
            <Image
              source={{ uri: item.owner.avatar }}
              style={styles.ownerAvatar}
            />
            <View style={styles.ownerDetails}>
              <Text style={[styles.ownerName, { color: colors.text }]}>
                {item.owner.name}
              </Text>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color="#FFD700" />
                <Text style={[styles.rating, { color: colors.text }]}>
                  {item.owner.rating}
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Description */}
        <View style={styles.descriptionContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Description
          </Text>
          <Text style={[styles.descriptionText, { color: colors.text + 'E6' }]}>
            {item.description}
          </Text>
        </View>
      </ScrollView>

      {/* Footer */}
      <View
        style={[
          styles.footer,
          { backgroundColor: colors.background, borderTopColor: colors.border },
        ]}
      >
        <TouchableOpacity
          style={[
            styles.button,
            styles.messageButton,
            { backgroundColor: colors.card },
          ]}
          onPress={handleMessage}
        >
          <Text style={[styles.buttonText, { color: colors.text }]}>
            Message Owner
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            styles.button,
            styles.tradeButton,
            { backgroundColor: colors.primary },
          ]}
          onPress={handleTradeRequest}
        >
          <Text style={[styles.buttonText, { color: colors.buttonText }]}>
            Offer Trade
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingTop: 50,
    paddingBottom: 10,
  },
  backButton: {
    padding: 8,
  },
  likeButton: {
    padding: 8,
  },
  imageContainer: {
    width: '100%',
    height: 300,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    padding: 20,
    borderBottomWidth: 1,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  price: {
    fontSize: 22,
    fontWeight: '600',
    marginBottom: 15,
  },
  infoRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  label: {
    width: 100,
    fontSize: 16,
  },
  value: {
    fontSize: 16,
    fontWeight: '500',
  },
  ownerContainer: {
    padding: 20,
    borderBottomWidth: 1,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  ownerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ownerAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 15,
  },
  ownerDetails: {
    flex: 1,
  },
  ownerName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 5,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rating: {
    marginLeft: 5,
    fontSize: 14,
  },
  descriptionContainer: {
    padding: 20,
  },
  descriptionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    flexDirection: 'row',
    padding: 15,
    borderTopWidth: 1,
  },
  button: {
    flex: 1,
    paddingVertical: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  messageButton: {
    marginRight: 10,
  },
  tradeButton: {},
  buttonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ItemDetailsScreen;
