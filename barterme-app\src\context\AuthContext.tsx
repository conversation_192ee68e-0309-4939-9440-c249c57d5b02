import React, {
  createContext,
  useState,
  useEffect,
  useContext,
  ReactNode,
} from 'react';
import supabase from '../utils/supabaseClient';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Session, User } from '@supabase/supabase-js';

// Define the shape of our auth context
interface AuthContextType {
  user: User | null;
  session: Session | null;
  loading: boolean;
  onboardingCompleted: boolean;
  signUp: (
    email: string,
    password: string,
    username: string
  ) => Promise<{ success: boolean; data?: any; error?: string }>;
  signIn: (
    email: string,
    password: string
  ) => Promise<{ success: boolean; data?: any; error?: string }>;
  signOut: () => Promise<{ success: boolean; error?: string }>;
  resetPassword: (
    email: string
  ) => Promise<{ success: boolean; data?: any; error?: string }>;
  updateProfile: (
    updates: Record<string, any>
  ) => Promise<{ success: boolean; data?: any; error?: string }>;
  completeOnboarding: () => Promise<{ success: boolean; error?: string }>;
  resetOnboarding: () => Promise<{ success: boolean; error?: string }>;
}

// Create auth context with default values
const AuthContext = createContext<AuthContextType>({
  user: null,
  session: null,
  loading: true,
  onboardingCompleted: false,
  signUp: async () => ({ success: false }),
  signIn: async () => ({ success: false }),
  signOut: async () => ({ success: false }),
  resetPassword: async () => ({ success: false }),
  updateProfile: async () => ({ success: false }),
  completeOnboarding: async () => ({ success: false }),
  resetOnboarding: async () => ({ success: false }),
});

// Props for the auth provider
interface AuthProviderProps {
  children: ReactNode;
}

// Auth provider component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [onboardingCompleted, setOnboardingCompleted] =
    useState<boolean>(false);

  // Check if user has completed onboarding
  useEffect(() => {
    const checkOnboarding = async () => {
      try {
        // For development: Always show the onboarding screen
        await AsyncStorage.removeItem('@onboarding_completed');
        setOnboardingCompleted(false);

        // Original code (commented out):
        // const value = await AsyncStorage.getItem('@onboarding_completed');
        // setOnboardingCompleted(value === 'true');
      } catch (e) {
        console.error('Failed to load onboarding status', e);
      }
    };

    checkOnboarding();
  }, []);

  // Listen for authentication state changes
  useEffect(() => {
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (event, currentSession) => {
        console.log('Auth state changed:', event);
        setSession(currentSession);
        setUser(currentSession?.user || null);
        setLoading(false);
      }
    );

    // Initial session check
    supabase.auth.getSession().then(({ data: { session: currentSession } }) => {
      setSession(currentSession);
      setUser(currentSession?.user || null);
      setLoading(false);
    });

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);

  // Sign up with email and password
  const signUp = async (email: string, password: string, username: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            username,
          },
        },
      });

      if (error) throw error;
      return { success: true, data };
    } catch (error: any) {
      console.error('Error signing up:', error.message);
      return { success: false, error: error.message };
    }
  };

  // Sign in with email and password
  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { success: true, data };
    } catch (error: any) {
      console.error('Error signing in:', error.message);
      return { success: false, error: error.message };
    }
  };

  // Sign out
  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      return { success: true };
    } catch (error: any) {
      console.error('Error signing out:', error.message);
      return { success: false, error: error.message };
    }
  };

  // Reset password
  const resetPassword = async (email: string) => {
    try {
      const { data, error } = await supabase.auth.resetPasswordForEmail(email);
      if (error) throw error;
      return { success: true, data };
    } catch (error: any) {
      console.error('Error resetting password:', error.message);
      return { success: false, error: error.message };
    }
  };

  // Update user profile
  const updateProfile = async (updates: Record<string, any>) => {
    try {
      const { data, error } = await supabase.auth.updateUser({
        data: updates,
      });

      if (error) throw error;
      return { success: true, data };
    } catch (error: any) {
      console.error('Error updating profile:', error.message);
      return { success: false, error: error.message };
    }
  };

  // Complete onboarding
  const completeOnboarding = async () => {
    try {
      await AsyncStorage.setItem('@onboarding_completed', 'true');
      setOnboardingCompleted(true);
      return { success: true };
    } catch (e: any) {
      console.error('Failed to save onboarding status', e);
      return { success: false, error: e.message };
    }
  };

  // Reset onboarding (for testing)
  const resetOnboarding = async () => {
    try {
      await AsyncStorage.removeItem('@onboarding_completed');
      setOnboardingCompleted(false);
      return { success: true };
    } catch (e: any) {
      console.error('Failed to reset onboarding status', e);
      return { success: false, error: e.message };
    }
  };

  // Return the context provider
  return (
    <AuthContext.Provider
      value={{
        user,
        session,
        loading,
        onboardingCompleted,
        signUp,
        signIn,
        signOut,
        resetPassword,
        updateProfile,
        completeOnboarding,
        resetOnboarding,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use the auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
