import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type TradesScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

// Placeholder trade data
const TRADES = [
  {
    id: '1',
    status: 'pending',
    date: '2023-09-15',
    yourItem: {
      id: '101',
      title: 'Vintage Camera',
      image:
        'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    },
    theirItem: {
      id: '201',
      title: 'Mechanical Keyboard',
      image:
        'https://images.unsplash.com/photo-1595225476474-57a67f26cf5d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
    },
    user: {
      id: 'user456',
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    },
  },
  {
    id: '2',
    status: 'accepted',
    date: '2023-09-10',
    yourItem: {
      id: '102',
      title: 'Mountain Bike',
      image:
        'https://images.unsplash.com/photo-1532298229144-0ec0c57515c7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1008&q=80',
    },
    theirItem: {
      id: '202',
      title: 'Laptop',
      image:
        'https://images.unsplash.com/photo-1593642702821-c8da6771f0c6?ixlib=rb-1.2.1&auto=format&fit=crop&w=1489&q=80',
    },
    user: {
      id: 'user789',
      name: 'John Doe',
      avatar: 'https://randomuser.me/api/portraits/men/85.jpg',
    },
  },
  {
    id: '3',
    status: 'declined',
    date: '2023-09-05',
    yourItem: {
      id: '103',
      title: 'Vintage Leather Jacket',
      image:
        'https://images.unsplash.com/photo-1551028719-00167b16eac5?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    },
    theirItem: {
      id: '203',
      title: 'Gaming Console',
      image:
        'https://images.unsplash.com/photo-1605901309584-818e25960a8f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1489&q=80',
    },
    user: {
      id: 'user101',
      name: 'Emma Wilson',
      avatar: 'https://randomuser.me/api/portraits/women/63.jpg',
    },
  },
];

const TradesScreen: React.FC = () => {
  const navigation = useNavigation<TradesScreenNavigationProp>();
  const { colors } = useTheme();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return '#F9A825'; // Amber
      case 'accepted':
        return '#4CAF50'; // Green
      case 'declined':
        return '#F44336'; // Red
      default:
        return colors.text;
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'pending':
        return 'Pending';
      case 'accepted':
        return 'Accepted';
      case 'declined':
        return 'Declined';
      default:
        return status;
    }
  };

  const handleTradePress = (tradeId: string) => {
    navigation.navigate('TradeOffer', { itemId: tradeId });
  };

  const handleChatPress = (userId: string) => {
    navigation.navigate('Chat', { userId });
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Your Trades</Text>
      </View>

      <FlatList
        data={TRADES}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.card, { backgroundColor: colors.card }]}
            onPress={() => handleTradePress(item.id)}
          >
            <View style={styles.cardHeader}>
              <View style={styles.userInfo}>
                <Image
                  source={{ uri: item.user.avatar }}
                  style={styles.avatar}
                />
                <Text style={[styles.userName, { color: colors.text }]}>
                  {item.user.name}
                </Text>
              </View>
              <View
                style={[
                  styles.statusBadge,
                  { backgroundColor: getStatusColor(item.status) + '20' },
                ]}
              >
                <Text
                  style={[
                    styles.statusText,
                    { color: getStatusColor(item.status) },
                  ]}
                >
                  {getStatusLabel(item.status)}
                </Text>
              </View>
            </View>

            <Text style={[styles.dateText, { color: colors.text + '80' }]}>
              {item.date}
            </Text>

            <View style={styles.tradeItems}>
              <View style={styles.itemContainer}>
                <Image
                  source={{ uri: item.yourItem.image }}
                  style={styles.itemImage}
                />
                <Text style={[styles.itemLabel, { color: colors.text + '80' }]}>
                  Your item
                </Text>
                <Text
                  style={[styles.itemTitle, { color: colors.text }]}
                  numberOfLines={1}
                >
                  {item.yourItem.title}
                </Text>
              </View>

              <View style={styles.arrowContainer}>
                <Ionicons
                  name="swap-horizontal"
                  size={24}
                  color={colors.primary}
                />
              </View>

              <View style={styles.itemContainer}>
                <Image
                  source={{ uri: item.theirItem.image }}
                  style={styles.itemImage}
                />
                <Text style={[styles.itemLabel, { color: colors.text + '80' }]}>
                  Their item
                </Text>
                <Text
                  style={[styles.itemTitle, { color: colors.text }]}
                  numberOfLines={1}
                >
                  {item.theirItem.title}
                </Text>
              </View>
            </View>

            {item.status === 'pending' && (
              <View style={styles.actions}>
                <TouchableOpacity
                  style={[
                    styles.actionButton,
                    { backgroundColor: colors.primary },
                  ]}
                  onPress={() => handleChatPress(item.user.id)}
                >
                  <Ionicons
                    name="chatbubble-outline"
                    size={18}
                    color={colors.buttonText}
                  />
                  <Text
                    style={[styles.actionText, { color: colors.buttonText }]}
                  >
                    Message
                  </Text>
                </TouchableOpacity>
              </View>
            )}
          </TouchableOpacity>
        )}
        contentContainerStyle={styles.list}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  list: {
    paddingHorizontal: 15,
    paddingBottom: 20,
  },
  card: {
    borderRadius: 15,
    marginVertical: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 10,
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  dateText: {
    fontSize: 12,
    marginBottom: 15,
  },
  tradeItems: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  itemContainer: {
    flex: 1,
    alignItems: 'center',
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
    marginBottom: 8,
  },
  itemLabel: {
    fontSize: 12,
    marginBottom: 4,
  },
  itemTitle: {
    fontSize: 14,
    fontWeight: '500',
    textAlign: 'center',
  },
  arrowContainer: {
    paddingHorizontal: 10,
  },
  actions: {
    marginTop: 15,
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  actionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 5,
  },
});

export default TradesScreen;
