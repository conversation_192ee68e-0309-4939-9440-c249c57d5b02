{"compilerOptions": {"target": "ES2020", "module": "commonjs", "lib": ["es2020"], "outDir": "./dist", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"@controllers/*": ["src/controllers/*"], "@middleware/*": ["src/middleware/*"], "@models/*": ["src/models/*"], "@routes/*": ["src/routes/*"], "@services/*": ["src/services/*"], "@utils/*": ["src/utils/*"], "@config/*": ["src/config/*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}