import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type DiscoverScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;

// Placeholder item data
const ITEMS = [
  {
    id: '1',
    title: 'Vintage Leather Jacket',
    price: '$120',
    condition: 'Excellent',
    image:
      'https://images.unsplash.com/photo-**********-00167b16eac5?ixlib=rb-1.2.1&auto=format&fit=crop&w=634&q=80',
    owner: {
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    },
  },
  {
    id: '2',
    title: 'Mechanical Keyboard',
    price: '$85',
    condition: 'Good',
    image:
      'https://images.unsplash.com/photo-1595225476474-57a67f26cf5d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
    owner: {
      name: 'Sarah Miller',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    },
  },
  {
    id: '3',
    title: 'Vintage Camera',
    price: '$95',
    condition: 'Good',
    image:
      'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    owner: {
      name: 'John Doe',
      avatar: 'https://randomuser.me/api/portraits/men/85.jpg',
    },
  },
  {
    id: '4',
    title: 'Mountain Bike',
    price: '$250',
    condition: 'Like New',
    image:
      'https://images.unsplash.com/photo-1532298229144-0ec0c57515c7?ixlib=rb-1.2.1&auto=format&fit=crop&w=1008&q=80',
    owner: {
      name: 'Emma Wilson',
      avatar: 'https://randomuser.me/api/portraits/women/63.jpg',
    },
  },
];

const { width } = Dimensions.get('window');
const CARD_WIDTH = width * 0.9;

const DiscoverScreen: React.FC = () => {
  const navigation = useNavigation<DiscoverScreenNavigationProp>();
  const { colors } = useTheme();

  const handleItemPress = (itemId: string) => {
    navigation.navigate('ItemDetails', { itemId });
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>Discover</Text>
        <TouchableOpacity style={styles.filterButton}>
          <Ionicons name="options-outline" size={24} color={colors.text} />
        </TouchableOpacity>
      </View>

      <FlatList
        data={ITEMS}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.card, { backgroundColor: colors.card }]}
            onPress={() => handleItemPress(item.id)}
          >
            <Image source={{ uri: item.image }} style={styles.image} />
            <View style={styles.cardContent}>
              <Text style={[styles.itemTitle, { color: colors.text }]}>
                {item.title}
              </Text>
              <Text style={[styles.itemPrice, { color: colors.primary }]}>
                {item.price}
              </Text>
              <Text
                style={[styles.itemCondition, { color: colors.text + '80' }]}
              >
                Condition: {item.condition}
              </Text>

              <View style={styles.ownerInfo}>
                <Image
                  source={{ uri: item.owner.avatar }}
                  style={styles.ownerAvatar}
                />
                <Text style={[styles.ownerName, { color: colors.text }]}>
                  {item.owner.name}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        )}
        contentContainerStyle={styles.list}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 20,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
  },
  filterButton: {
    padding: 8,
  },
  list: {
    paddingHorizontal: 15,
    paddingBottom: 20,
  },
  card: {
    width: CARD_WIDTH,
    borderRadius: 15,
    marginVertical: 10,
    alignSelf: 'center',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
  },
  image: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  cardContent: {
    padding: 15,
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  itemCondition: {
    fontSize: 14,
    marginBottom: 12,
  },
  ownerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  ownerAvatar: {
    width: 30,
    height: 30,
    borderRadius: 15,
    marginRight: 8,
  },
  ownerName: {
    fontSize: 14,
  },
});

export default DiscoverScreen;
