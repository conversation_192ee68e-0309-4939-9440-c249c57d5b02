{"name": "simpleapp", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/slider": "^4.5.6", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@supabase/supabase-js": "^2.49.4", "expo": "~52.0.43", "expo-status-bar": "~2.0.1", "react": "18.3.1", "react-native": "0.76.9", "react-native-dotenv": "^3.4.11", "react-native-gesture-handler": "^2.25.0", "react-native-reanimated": "^3.17.3", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-url-polyfill": "^2.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.3.12", "typescript": "^5.3.3"}, "private": true}