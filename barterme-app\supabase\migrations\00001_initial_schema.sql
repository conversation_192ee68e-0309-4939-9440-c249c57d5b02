-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users Table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  username TEXT UNIQUE NOT NULL,
  display_name TEXT,
  avatar_url TEXT,
  bio TEXT,
  location TEXT,
  reputation_score NUMERIC DEFAULT 0,
  is_verified BOOLEAN DEFAULT FALSE
);

-- Items Table
CREATE TABLE items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  condition TEXT NOT NULL,
  category TEXT NOT NULL,
  subcategory TEXT,
  estimated_value NUMERIC,
  is_available BOOLEAN DEFAULT TRUE,
  is_featured BOOLEAN DEFAULT FALSE,
  view_count INTEGER DEFAULT 0
);

-- Item Images Table
CREATE TABLE item_images (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  item_id UUID REFERENCES items(id) ON DELETE CASCADE,
  storage_path TEXT NOT NULL,
  display_order INTEGER DEFAULT 0
);

-- Trades Table
CREATE TABLE trades (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  initiator_id UUID REFERENCES users(id),
  responder_id UUID REFERENCES users(id),
  status TEXT DEFAULT 'pending', -- pending, accepted, rejected, completed, cancelled
  is_public BOOLEAN DEFAULT FALSE,
  cash_amount NUMERIC DEFAULT 0,
  cash_direction TEXT, -- 'initiator_to_responder' or 'responder_to_initiator'
  completion_date TIMESTAMP WITH TIME ZONE
);

-- Trade Items Table
CREATE TABLE trade_items (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  trade_id UUID REFERENCES trades(id) ON DELETE CASCADE,
  item_id UUID REFERENCES items(id),
  offered_by UUID REFERENCES users(id),
  is_confirmed BOOLEAN DEFAULT FALSE
);

-- Messages Table
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  trade_id UUID REFERENCES trades(id) ON DELETE CASCADE,
  sender_id UUID REFERENCES users(id),
  content TEXT NOT NULL,
  is_read BOOLEAN DEFAULT FALSE
);

-- Trade Reactions Table
CREATE TABLE trade_reactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  trade_id UUID REFERENCES trades(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id),
  reaction TEXT NOT NULL, -- emoji code
  UNIQUE(trade_id, user_id, reaction)
);

-- Trade Votes Table
CREATE TABLE trade_votes (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  trade_id UUID REFERENCES trades(id) ON DELETE CASCADE,
  voter_id UUID REFERENCES users(id),
  winner_id UUID REFERENCES users(id),
  UNIQUE(trade_id, voter_id)
);

-- Create Row Level Security (RLS) policies

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE items ENABLE ROW LEVEL SECURITY;
ALTER TABLE item_images ENABLE ROW LEVEL SECURITY;
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;
ALTER TABLE trade_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE trade_reactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE trade_votes ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view all profiles" 
ON users FOR SELECT 
USING (true);

CREATE POLICY "Users can update own profile" 
ON users FOR UPDATE 
USING (auth.uid() = id);

-- Items table policies
CREATE POLICY "Anyone can view available items" 
ON items FOR SELECT 
USING (is_available = true);

CREATE POLICY "Users can view their unavailable items" 
ON items FOR SELECT 
USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own items" 
ON items FOR INSERT 
WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own items" 
ON items FOR UPDATE 
USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own items" 
ON items FOR DELETE 
USING (auth.uid() = user_id);

-- Item Images policies
CREATE POLICY "Anyone can view item images" 
ON item_images FOR SELECT 
USING (true);

CREATE POLICY "Users can insert images for their items" 
ON item_images FOR INSERT 
WITH CHECK (EXISTS (
  SELECT 1 FROM items 
  WHERE items.id = item_id AND items.user_id = auth.uid()
));

CREATE POLICY "Users can delete images for their items" 
ON item_images FOR DELETE 
USING (EXISTS (
  SELECT 1 FROM items 
  WHERE items.id = item_id AND items.user_id = auth.uid()
));

-- Trades policies
CREATE POLICY "Users can view their trades" 
ON trades FOR SELECT 
USING (auth.uid() = initiator_id OR auth.uid() = responder_id);

CREATE POLICY "Anyone can view public trades" 
ON trades FOR SELECT 
USING (is_public = true);

CREATE POLICY "Users can insert trades they initiate" 
ON trades FOR INSERT 
WITH CHECK (auth.uid() = initiator_id);

CREATE POLICY "Users involved can update trade" 
ON trades FOR UPDATE 
USING (auth.uid() = initiator_id OR auth.uid() = responder_id);

-- Trade Items policies
CREATE POLICY "Users can view trade items for their trades" 
ON trade_items FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM trades 
  WHERE trades.id = trade_id AND (trades.initiator_id = auth.uid() OR trades.responder_id = auth.uid() OR trades.is_public = true)
));

CREATE POLICY "Users can insert items into their trades" 
ON trade_items FOR INSERT 
WITH CHECK (
  auth.uid() = offered_by AND 
  EXISTS (
    SELECT 1 FROM trades 
    WHERE trades.id = trade_id AND (trades.initiator_id = auth.uid() OR trades.responder_id = auth.uid())
  )
);

CREATE POLICY "Users can update confirmation of their trade items" 
ON trade_items FOR UPDATE 
USING (
  EXISTS (
    SELECT 1 FROM trades 
    WHERE trades.id = trade_id AND (trades.initiator_id = auth.uid() OR trades.responder_id = auth.uid())
  )
);

-- Messages policies
CREATE POLICY "Users can view messages in their trades" 
ON messages FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM trades 
  WHERE trades.id = trade_id AND (trades.initiator_id = auth.uid() OR trades.responder_id = auth.uid())
));

CREATE POLICY "Users can insert messages in their trades" 
ON messages FOR INSERT 
WITH CHECK (
  auth.uid() = sender_id AND 
  EXISTS (
    SELECT 1 FROM trades 
    WHERE trades.id = trade_id AND (trades.initiator_id = auth.uid() OR trades.responder_id = auth.uid())
  )
);

-- Trade Reactions policies
CREATE POLICY "Anyone can view reactions on public trades" 
ON trade_reactions FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM trades 
  WHERE trades.id = trade_id AND (trades.is_public = true OR trades.initiator_id = auth.uid() OR trades.responder_id = auth.uid())
));

CREATE POLICY "Users can add reactions to public trades" 
ON trade_reactions FOR INSERT 
WITH CHECK (
  auth.uid() = user_id AND 
  EXISTS (
    SELECT 1 FROM trades 
    WHERE trades.id = trade_id AND trades.is_public = true
  )
);

CREATE POLICY "Users can delete their own reactions" 
ON trade_reactions FOR DELETE 
USING (auth.uid() = user_id);

-- Trade Votes policies
CREATE POLICY "Anyone can view votes on public trades" 
ON trade_votes FOR SELECT 
USING (EXISTS (
  SELECT 1 FROM trades 
  WHERE trades.id = trade_id AND trades.is_public = true
));

CREATE POLICY "Users can vote on public trades" 
ON trade_votes FOR INSERT 
WITH CHECK (
  auth.uid() = voter_id AND 
  EXISTS (
    SELECT 1 FROM trades 
    WHERE trades.id = trade_id AND trades.is_public = true
  )
);

CREATE POLICY "Users can update their own votes" 
ON trade_votes FOR UPDATE 
USING (auth.uid() = voter_id); 