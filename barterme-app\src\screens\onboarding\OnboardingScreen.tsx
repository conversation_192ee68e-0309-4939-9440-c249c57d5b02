import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  FlatList,
  Animated,
  Image,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../context/AuthContext';
import { RootStackParamList } from '../../navigation/AppNavigator';
import { useTheme } from '../../theme/ThemeProvider';

type OnboardingNavigationProp = NativeStackNavigationProp<
  RootStackParamList,
  'Onboarding'
>;

const { width, height } = Dimensions.get('window');

interface OnboardingItem {
  id: number;
  title: string;
  description: string;
  icon: string;
  backgroundColor: string;
  iconColor: string;
  useDarkLogo: boolean; // Whether to use dark or white logo
}

const onboardingData: OnboardingItem[] = [
  {
    id: 1,
    title: 'Welcome to BarterMe',
    description:
      'The next-gen marketplace for trading items you no longer need for things you want.',
    icon: 'swap-horizontal',
    backgroundColor: '#4A6FFF',
    iconColor: '#FFFFFF',
    useDarkLogo: false, // Blue background - use white logo
  },
  {
    id: 2,
    title: 'Discover Items',
    description:
      'Browse through thousands of items available for trade in your area.',
    icon: 'search',
    backgroundColor: '#FF6B6B',
    iconColor: '#FFFFFF',
    useDarkLogo: false, // Red background - use white logo
  },
  {
    id: 3,
    title: 'Chat & Negotiate',
    description:
      'Communicate directly with other users to negotiate trades that work for both of you.',
    icon: 'chatbubbles',
    backgroundColor: '#4ECDC4',
    iconColor: '#FFFFFF',
    useDarkLogo: true, // Light teal background - use black logo
  },
  {
    id: 4,
    title: 'Make Offers',
    description: 'Create trade offers by selecting items you want to exchange.',
    icon: 'gift',
    backgroundColor: '#FFC857',
    iconColor: '#FFFFFF',
    useDarkLogo: true, // Yellow background - use black logo
  },
  {
    id: 5,
    title: 'Join Our Community',
    description:
      'Be part of a sustainable community focused on reusing and reducing waste.',
    icon: 'people',
    backgroundColor: '#7E57C2',
    iconColor: '#FFFFFF',
    useDarkLogo: false, // Purple background - use white logo
  },
];

export default function OnboardingScreen() {
  const navigation = useNavigation<OnboardingNavigationProp>();
  const { completeOnboarding } = useAuth();
  const [currentIndex, setCurrentIndex] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const scrollX = useRef(new Animated.Value(0)).current;
  const slideTimer = useRef<NodeJS.Timeout | null>(null);

  // Auto advance slides every 5 seconds
  useEffect(() => {
    startAutoScroll();
    return () => {
      if (slideTimer.current) {
        clearTimeout(slideTimer.current);
      }
    };
  }, [currentIndex]);

  const startAutoScroll = () => {
    if (slideTimer.current) {
      clearTimeout(slideTimer.current);
    }

    slideTimer.current = setTimeout(() => {
      if (currentIndex < onboardingData.length - 1) {
        scrollTo(currentIndex + 1);
      } else {
        scrollTo(0); // Loop back to the first slide
      }
    }, 5000);
  };

  const viewableItemsChanged = useRef(({ viewableItems }: any) => {
    if (viewableItems[0]) {
      setCurrentIndex(viewableItems[0].index);
    }
  }).current;

  const viewConfig = useRef({
    viewAreaCoveragePercentThreshold: 50,
  }).current;

  const scrollTo = (index: number) => {
    if (flatListRef.current) {
      flatListRef.current.scrollToIndex({ index, animated: true });
    }
  };

  const handleLogin = async () => {
    await completeOnboarding();
    navigation.navigate('Auth');
  };

  const handleSignup = async () => {
    await completeOnboarding();
    navigation.navigate('Auth');
  };

  return (
    <View style={styles.container}>
      <StatusBar
        barStyle="light-content"
        backgroundColor={onboardingData[currentIndex]?.backgroundColor}
        translucent
      />

      <SafeAreaView
        style={[
          styles.topBar,
          { backgroundColor: onboardingData[currentIndex]?.backgroundColor },
        ]}
      >
        <TouchableOpacity style={styles.skipButton} onPress={handleLogin}>
          <Text style={styles.skipButtonText}>Login</Text>
        </TouchableOpacity>
      </SafeAreaView>

      <FlatList
        ref={flatListRef}
        data={onboardingData}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.slide, { backgroundColor: item.backgroundColor }]}
            activeOpacity={0.9}
            onPress={() => {
              if (currentIndex < onboardingData.length - 1) {
                scrollTo(currentIndex + 1);
              } else {
                scrollTo(0);
              }
            }}
          >
            <View style={styles.logoContainer}>
              <Image
                source={
                  item.useDarkLogo
                    ? require('../../../assets/images/logoBlackText.png')
                    : require('../../../assets/images/logoWhiteText.png')
                }
                style={styles.logo}
                resizeMode="contain"
              />
            </View>

            <View style={styles.iconContainer}>
              <Ionicons
                name={item.icon as any}
                size={120}
                color={item.iconColor}
              />
            </View>

            <View style={styles.textContainer}>
              <Text style={styles.title}>{item.title}</Text>
              <Text style={styles.description}>{item.description}</Text>
            </View>
          </TouchableOpacity>
        )}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onViewableItemsChanged={viewableItemsChanged}
        viewabilityConfig={viewConfig}
        onScroll={Animated.event(
          [{ nativeEvent: { contentOffset: { x: scrollX } } }],
          { useNativeDriver: false }
        )}
        scrollEventThrottle={16}
        bounces={false}
        keyExtractor={(item) => item.id.toString()}
      />

      {/* Overlaid UI elements */}
      <View style={styles.overlay}>
        <View style={styles.paginationContainer}>
          {onboardingData.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.dot, index === currentIndex && styles.activeDot]}
              onPress={() => scrollTo(index)}
            />
          ))}
        </View>

        <View style={styles.buttonsContainer}>
          <TouchableOpacity
            style={[styles.button, styles.loginButton]}
            onPress={handleLogin}
          >
            <Text style={styles.buttonText}>Log In</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.button, styles.signupButton]}
            onPress={handleSignup}
          >
            <Text style={styles.signupButtonText}>Sign Up</Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    paddingTop: StatusBar.currentHeight || 40,
    paddingBottom: 10,
    paddingHorizontal: 20,
  },
  skipButton: {
    padding: 10,
  },
  skipButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
  },
  slide: {
    width,
    height,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  logoContainer: {
    position: 'absolute',
    top: 80,
    alignItems: 'center',
  },
  logo: {
    width: 240,
    height: 80,
  },
  iconContainer: {
    width: 200,
    height: 200,
    borderRadius: 100,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 40,
  },
  textContainer: {
    alignItems: 'center',
    maxWidth: width * 0.8,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 18,
    color: '#FFFFFF',
    textAlign: 'center',
    lineHeight: 26,
    opacity: 0.9,
  },
  overlay: {
    position: 'absolute',
    bottom: 50,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
  paginationContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
  },
  dot: {
    width: 50,
    height: 12,
    borderRadius: 25,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 6,
  },
  activeDot: {
    width: 80,
    backgroundColor: '#FFFFFF',
  },
  buttonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '80%',
  },
  button: {
    paddingVertical: 15,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    width: '48%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  loginButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  signupButton: {
    backgroundColor: '#FFFFFF',
  },
  buttonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  signupButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#4A6FFF',
  },
});
