import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Image,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type EditProfileScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;

const EditProfileScreen: React.FC = () => {
  const navigation = useNavigation<EditProfileScreenNavigationProp>();
  const { colors, isDark } = useTheme();

  // Placeholder user data
  const [userData, setUserData] = useState({
    fullName: '<PERSON>',
    username: 'johndoe123',
    bio: 'I love trading vintage items and collectibles. Always looking for unique finds!',
    email: '<EMAIL>',
    phone: '(*************',
    location: 'San Francisco, CA',
    profilePicture:
      'https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
  });

  const handleSave = () => {
    // In a real app, we would save the data to the server here
    navigation.goBack();
  };

  const handlePickImage = () => {
    // In a real app, we would implement image picker functionality here
    console.log('Picking image...');
  };

  const InputField = ({
    label,
    value,
    onChangeText,
    multiline = false,
    keyboardType = 'default',
  }: {
    label: string;
    value: string;
    onChangeText: (text: string) => void;
    multiline?: boolean;
    keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  }) => (
    <View style={styles.inputContainer}>
      <Text style={[styles.inputLabel, { color: colors.text }]}>{label}</Text>
      <TextInput
        style={[
          styles.input,
          multiline && styles.multilineInput,
          {
            color: colors.text,
            backgroundColor: colors.card,
            borderColor: colors.border,
          },
        ]}
        value={value}
        onChangeText={onChangeText}
        placeholderTextColor={colors.text + '80'}
        multiline={multiline}
        keyboardType={keyboardType}
      />
    </View>
  );

  return (
    <KeyboardAvoidingView
      style={{ flex: 1 }}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={[styles.header, { backgroundColor: colors.card }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          <Text style={[styles.headerTitle, { color: colors.text }]}>
            Edit Profile
          </Text>
          <TouchableOpacity style={styles.saveButton} onPress={handleSave}>
            <Text style={[styles.saveButtonText, { color: colors.primary }]}>
              Save
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          <View style={styles.profilePictureContainer}>
            <Image
              source={{ uri: userData.profilePicture }}
              style={styles.profilePicture}
            />
            <TouchableOpacity
              style={[
                styles.editPictureButton,
                { backgroundColor: colors.primary },
              ]}
              onPress={handlePickImage}
            >
              <Ionicons name="camera" size={20} color="#FFFFFF" />
            </TouchableOpacity>
          </View>

          <InputField
            label="Full Name"
            value={userData.fullName}
            onChangeText={(text) =>
              setUserData({ ...userData, fullName: text })
            }
          />

          <InputField
            label="Username"
            value={userData.username}
            onChangeText={(text) =>
              setUserData({ ...userData, username: text })
            }
          />

          <InputField
            label="Bio"
            value={userData.bio}
            onChangeText={(text) => setUserData({ ...userData, bio: text })}
            multiline
          />

          <InputField
            label="Email"
            value={userData.email}
            onChangeText={(text) => setUserData({ ...userData, email: text })}
            keyboardType="email-address"
          />

          <InputField
            label="Phone"
            value={userData.phone}
            onChangeText={(text) => setUserData({ ...userData, phone: text })}
            keyboardType="phone-pad"
          />

          <InputField
            label="Location"
            value={userData.location}
            onChangeText={(text) =>
              setUserData({ ...userData, location: text })
            }
          />

          <View style={styles.sectionDivider}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Account Settings
            </Text>
          </View>

          <TouchableOpacity
            style={[styles.settingsButton, { backgroundColor: colors.card }]}
            onPress={() => console.log('Change Password')}
          >
            <Ionicons
              name="lock-closed-outline"
              size={22}
              color={colors.text}
              style={styles.settingsIcon}
            />
            <Text style={[styles.settingsText, { color: colors.text }]}>
              Change Password
            </Text>
            <Ionicons
              name="chevron-forward"
              size={18}
              color={colors.text + '80'}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingsButton, { backgroundColor: colors.card }]}
            onPress={() => console.log('Notification Settings')}
          >
            <Ionicons
              name="notifications-outline"
              size={22}
              color={colors.text}
              style={styles.settingsIcon}
            />
            <Text style={[styles.settingsText, { color: colors.text }]}>
              Notification Settings
            </Text>
            <Ionicons
              name="chevron-forward"
              size={18}
              color={colors.text + '80'}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.settingsButton, { backgroundColor: colors.card }]}
            onPress={() => console.log('Privacy Settings')}
          >
            <Ionicons
              name="shield-outline"
              size={22}
              color={colors.text}
              style={styles.settingsIcon}
            />
            <Text style={[styles.settingsText, { color: colors.text }]}>
              Privacy Settings
            </Text>
            <Ionicons
              name="chevron-forward"
              size={18}
              color={colors.text + '80'}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.logoutButton,
              { backgroundColor: isDark ? '#463652' : '#FFE4E4' },
            ]}
            onPress={() => console.log('Log out')}
          >
            <Ionicons
              name="log-out-outline"
              size={22}
              color={isDark ? '#FF6B6B' : '#FF3B30'}
              style={styles.settingsIcon}
            />
            <Text
              style={[
                styles.logoutText,
                { color: isDark ? '#FF6B6B' : '#FF3B30' },
              ]}
            >
              Log out
            </Text>
          </TouchableOpacity>
        </ScrollView>
      </View>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  backButton: {
    padding: 8,
  },
  saveButton: {
    padding: 8,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingBottom: 40,
  },
  profilePictureContainer: {
    alignItems: 'center',
    marginTop: 24,
    marginBottom: 30,
  },
  profilePicture: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  editPictureButton: {
    position: 'absolute',
    bottom: 0,
    right: '32%',
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
  },
  inputContainer: {
    marginBottom: 20,
  },
  inputLabel: {
    fontSize: 14,
    marginBottom: 8,
    fontWeight: '500',
  },
  input: {
    height: 50,
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 16,
    fontSize: 16,
  },
  multilineInput: {
    height: 100,
    paddingTop: 12,
    paddingBottom: 12,
    textAlignVertical: 'top',
  },
  sectionDivider: {
    marginTop: 10,
    marginBottom: 20,
    paddingTop: 10,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#CCCCCC50',
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 10,
  },
  settingsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  settingsIcon: {
    marginRight: 12,
  },
  settingsText: {
    fontSize: 16,
    flex: 1,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 15,
    paddingHorizontal: 16,
    borderRadius: 12,
    marginTop: 20,
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '500',
  },
});

export default EditProfileScreen;
