import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  FlatList,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type SearchScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;

interface SearchItem {
  id: string;
  name: string;
  description: string;
  image: string;
  price: string;
  owner: {
    id: string;
    name: string;
    avatar: string;
  };
  distance: string;
  category: string;
}

function SearchScreen() {
  const navigation = useNavigation<SearchScreenNavigationProp>();
  const { colors } = useTheme();

  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<SearchItem[]>([]);
  const [recentSearches, setRecentSearches] = useState<string[]>([
    'vintage camera',
    'Nintendo Switch',
    'iPhone',
    'mountain bike',
  ]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  // Mock categories for filtering
  const categories = [
    'Electronics',
    'Clothing',
    'Home',
    'Sports',
    'Books',
    'Games',
    'Collectibles',
  ];

  // Mock search results
  const mockItems: SearchItem[] = [
    {
      id: '1',
      name: 'Sony WH-1000XM4 Headphones',
      description: 'Noise cancelling wireless headphones, barely used',
      image:
        'https://images.unsplash.com/photo-1505751171710-1f6d0ace5a85?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      price: '$180',
      owner: {
        id: 'user123',
        name: 'Sarah Miller',
        avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      },
      distance: '2.5 miles',
      category: 'Electronics',
    },
    {
      id: '2',
      name: 'Nintendo Switch with Games',
      description: 'Nintendo Switch console with 3 games and all accessories',
      image:
        'https://images.unsplash.com/photo-1585857188898-7c3d2324837d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      price: '$250',
      owner: {
        id: 'user456',
        name: 'Michael Johnson',
        avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      },
      distance: '3 miles',
      category: 'Games',
    },
    {
      id: '3',
      name: 'Vintage Film Camera',
      description: 'Canon AE-1 35mm Film Camera, great condition',
      image:
        'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      price: '$120',
      owner: {
        id: 'user789',
        name: 'David Wilson',
        avatar: 'https://randomuser.me/api/portraits/men/52.jpg',
      },
      distance: '1.2 miles',
      category: 'Collectibles',
    },
    {
      id: '4',
      name: 'Mountain Bike',
      description: 'Trek mountain bike, 21 speed, recently tuned up',
      image:
        'https://images.unsplash.com/photo-1507035895480-2b3156c31fc8?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      price: '$350',
      owner: {
        id: 'user101',
        name: 'Emily Johnson',
        avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
      },
      distance: '4.7 miles',
      category: 'Sports',
    },
    {
      id: '5',
      name: 'Leather Jacket',
      description: 'Black leather jacket, size M, worn only a few times',
      image:
        'https://images.unsplash.com/photo-1551028719-00167b16eac5?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      price: '$90',
      owner: {
        id: 'user102',
        name: 'Jessica Davis',
        avatar: 'https://randomuser.me/api/portraits/women/10.jpg',
      },
      distance: '3.2 miles',
      category: 'Clothing',
    },
  ];

  // Handle search
  useEffect(() => {
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      setIsSearching(false);
      return;
    }

    setIsSearching(true);

    // Simulate API call delay
    const timer = setTimeout(() => {
      let results = mockItems.filter(
        (item) =>
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase())
      );

      if (selectedCategory) {
        results = results.filter((item) => item.category === selectedCategory);
      }

      setSearchResults(results);
      setIsSearching(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [searchQuery, selectedCategory]);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const handleItemPress = (itemId: string) => {
    navigation.navigate('ItemDetails', { itemId });
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setSearchResults([]);
  };

  const handleRecentSearchPress = (search: string) => {
    setSearchQuery(search);

    // Move to top of recent searches
    const updatedSearches = [
      search,
      ...recentSearches.filter((item) => item !== search),
    ].slice(0, 5);

    setRecentSearches(updatedSearches);
  };

  const handleCategoryPress = (category: string) => {
    if (selectedCategory === category) {
      setSelectedCategory(null);
    } else {
      setSelectedCategory(category);
    }
  };

  const renderItem = ({ item }: { item: SearchItem }) => (
    <TouchableOpacity
      style={[styles.itemCard, { backgroundColor: colors.card }]}
      onPress={() => handleItemPress(item.id)}
    >
      <Image source={{ uri: item.image }} style={styles.itemImage} />
      <View style={styles.itemContent}>
        <Text style={[styles.itemName, { color: colors.text }]}>
          {item.name}
        </Text>
        <Text
          style={[styles.itemDescription, { color: colors.text + '80' }]}
          numberOfLines={2}
        >
          {item.description}
        </Text>
        <View style={styles.itemFooter}>
          <Text style={[styles.itemPrice, { color: colors.primary }]}>
            {item.price}
          </Text>
          <View style={styles.itemOwner}>
            <Image
              source={{ uri: item.owner.avatar }}
              style={styles.ownerAvatar}
            />
            <Text style={[styles.distance, { color: colors.text + '80' }]}>
              {item.distance}
            </Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Search Header */}
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>

        <View
          style={[styles.searchContainer, { backgroundColor: colors.card }]}
        >
          <Ionicons name="search" size={20} color={colors.text + '80'} />
          <TextInput
            style={[styles.searchInput, { color: colors.text }]}
            placeholder="Search items..."
            placeholderTextColor={colors.text + '60'}
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoFocus
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={handleClearSearch}>
              <Ionicons
                name="close-circle"
                size={20}
                color={colors.text + '80'}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Categories */}
      <View style={styles.categoriesContainer}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categories}
        >
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryChip,
                {
                  backgroundColor:
                    selectedCategory === category
                      ? colors.primary
                      : colors.card,
                  borderColor: colors.border,
                },
              ]}
              onPress={() => handleCategoryPress(category)}
            >
              <Text
                style={[
                  styles.categoryText,
                  {
                    color:
                      selectedCategory === category ? '#FFFFFF' : colors.text,
                  },
                ]}
              >
                {category}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Search Results or Recent Searches */}
      {isSearching ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
        </View>
      ) : searchQuery.trim() === '' ? (
        <View style={styles.recentSearchesContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Recent Searches
          </Text>
          {recentSearches.map((search, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.recentSearchItem,
                { borderBottomColor: colors.border },
              ]}
              onPress={() => handleRecentSearchPress(search)}
            >
              <Ionicons
                name="time-outline"
                size={20}
                color={colors.text + '60'}
              />
              <Text style={[styles.recentSearchText, { color: colors.text }]}>
                {search}
              </Text>
              <Ionicons
                name="chevron-forward"
                size={20}
                color={colors.text + '60'}
              />
            </TouchableOpacity>
          ))}
        </View>
      ) : searchResults.length > 0 ? (
        <FlatList
          data={searchResults}
          renderItem={renderItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.searchResults}
        />
      ) : (
        <View style={styles.noResultsContainer}>
          <Ionicons
            name="search-outline"
            size={60}
            color={colors.text + '40'}
          />
          <Text style={[styles.noResultsText, { color: colors.text }]}>
            No results found
          </Text>
          <Text
            style={[styles.noResultsSubText, { color: colors.text + '80' }]}
          >
            Try a different search term or category
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
  },
  backButton: {
    padding: 8,
    marginRight: 8,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    height: 40,
    borderRadius: 20,
  },
  searchInput: {
    flex: 1,
    marginLeft: 8,
    fontSize: 16,
  },
  categoriesContainer: {
    paddingVertical: 12,
  },
  categories: {
    paddingHorizontal: 16,
  },
  categoryChip: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    borderWidth: 1,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '500',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recentSearchesContainer: {
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  recentSearchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  recentSearchText: {
    flex: 1,
    fontSize: 16,
    marginLeft: 12,
  },
  searchResults: {
    padding: 16,
  },
  itemCard: {
    flexDirection: 'row',
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 16,
  },
  itemImage: {
    width: 120,
    height: 120,
  },
  itemContent: {
    flex: 1,
    padding: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  itemDescription: {
    fontSize: 14,
    marginBottom: 8,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: '600',
  },
  itemOwner: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ownerAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
  },
  distance: {
    fontSize: 12,
  },
  noResultsContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  noResultsText: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
  },
  noResultsSubText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default SearchScreen;
