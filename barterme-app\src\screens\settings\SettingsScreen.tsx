import React, { useState } from 'react';
import {
  View,
  Text,
  Switch,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
} from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';

// Define settings section interface
interface SettingsSection {
  title: string;
  options: SettingsOption[];
}

// Define settings option interface
interface SettingsOption {
  title: string;
  type: 'toggle' | 'action' | 'link';
  value?: boolean;
  action?: () => void;
  icon?: string;
}

type SettingsScreenNavigationProp = NativeStackNavigationProp<any>;

function SettingsScreen() {
  const navigation = useNavigation<SettingsScreenNavigationProp>();
  const { isDark, toggleTheme } = useTheme();

  // Initialize settings with useState
  const [settings, setSettings] = useState<SettingsSection[]>([
    {
      title: 'Appearance',
      options: [
        {
          title: 'Dark Mode',
          type: 'toggle',
          value: isDark,
          action: toggleTheme,
          icon: 'moon-outline',
        },
      ],
    },
    {
      title: 'Notifications',
      options: [
        {
          title: 'Push Notifications',
          type: 'toggle',
          value: true,
          icon: 'notifications-outline',
        },
        {
          title: 'Email Notifications',
          type: 'toggle',
          value: false,
          icon: 'mail-outline',
        },
      ],
    },
    {
      title: 'Privacy & Security',
      options: [
        {
          title: 'Privacy Settings',
          type: 'link',
          icon: 'lock-closed-outline',
        },
        {
          title: 'Two-Factor Authentication',
          type: 'toggle',
          value: false,
          icon: 'shield-checkmark-outline',
        },
      ],
    },
    {
      title: 'Support',
      options: [
        {
          title: 'Contact Support',
          type: 'action',
          action: () =>
            Alert.alert(
              'Support',
              'Contact our support <NAME_EMAIL>'
            ),
          icon: 'help-circle-outline',
        },
        {
          title: 'Report a Bug',
          type: 'action',
          action: () =>
            Alert.alert('Report Bug', 'Thanks for helping us improve!'),
          icon: 'bug-outline',
        },
      ],
    },
    {
      title: 'Account',
      options: [
        {
          title: 'Logout',
          type: 'action',
          action: () =>
            Alert.alert('Logout', 'Are you sure you want to logout?', [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Logout', style: 'destructive' },
            ]),
          icon: 'log-out-outline',
        },
        {
          title: 'Delete Account',
          type: 'action',
          action: () =>
            Alert.alert('Delete Account', 'This action cannot be undone', [
              { text: 'Cancel', style: 'cancel' },
              { text: 'Delete', style: 'destructive' },
            ]),
          icon: 'trash-outline',
        },
      ],
    },
  ]);

  // Toggle handler for switches
  const handleToggle = (sectionIndex: number, optionIndex: number) => {
    const newSettings = [...settings];
    const currentValue = newSettings[sectionIndex].options[optionIndex].value;
    newSettings[sectionIndex].options[optionIndex].value = !currentValue;
    setSettings(newSettings);

    // If this is the dark mode toggle, also call the action
    if (sectionIndex === 0 && optionIndex === 0) {
      toggleTheme();
    }
  };

  const handleGoBack = () => {
    navigation.goBack();
  };

  return (
    <View
      style={[
        styles.container,
        { backgroundColor: isDark ? '#121212' : '#F5F5F5' },
      ]}
    >
      <View
        style={[
          styles.header,
          { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
        ]}
      >
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <Ionicons
            name="chevron-back"
            size={28}
            color={isDark ? '#FFFFFF' : '#000000'}
          />
        </TouchableOpacity>
        <Text
          style={[
            styles.headerTitle,
            { color: isDark ? '#FFFFFF' : '#000000' },
          ]}
        >
          Settings
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView style={styles.scrollView}>
        {settings.map((section, sectionIndex) => (
          <View
            key={`section-${sectionIndex}`}
            style={[
              styles.section,
              { backgroundColor: isDark ? '#1E1E1E' : '#FFFFFF' },
            ]}
          >
            <Text
              style={[
                styles.sectionTitle,
                { color: isDark ? '#BBBBBB' : '#757575' },
              ]}
            >
              {section.title}
            </Text>

            {section.options.map((option, optionIndex) => (
              <View
                key={`option-${sectionIndex}-${optionIndex}`}
                style={[
                  styles.option,
                  { borderBottomColor: isDark ? '#333333' : '#E0E0E0' },
                ]}
              >
                <View style={styles.optionLeft}>
                  {option.icon && (
                    <Ionicons
                      name={option.icon as any}
                      size={22}
                      color={isDark ? '#BBBBBB' : '#757575'}
                      style={styles.optionIcon}
                    />
                  )}
                  <Text
                    style={[
                      styles.optionTitle,
                      { color: isDark ? '#FFFFFF' : '#000000' },
                    ]}
                  >
                    {option.title}
                  </Text>
                </View>

                {option.type === 'toggle' && (
                  <Switch
                    value={option.value}
                    onValueChange={() =>
                      handleToggle(sectionIndex, optionIndex)
                    }
                    trackColor={{ false: '#767577', true: '#4CAF50' }}
                    thumbColor={option.value ? '#FFFFFF' : '#f4f3f4'}
                  />
                )}

                {option.type === 'link' && (
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={isDark ? '#BBBBBB' : '#757575'}
                  />
                )}

                {option.type === 'action' &&
                  option.title !== 'Logout' &&
                  option.title !== 'Delete Account' && (
                    <TouchableOpacity onPress={option.action}>
                      <Text style={styles.actionText}>
                        <Ionicons
                          name="chevron-forward"
                          size={20}
                          color={isDark ? '#BBBBBB' : '#757575'}
                        />
                      </Text>
                    </TouchableOpacity>
                  )}

                {option.type === 'action' && option.title === 'Logout' && (
                  <TouchableOpacity onPress={option.action}>
                    <Text style={styles.logoutText}>Logout</Text>
                  </TouchableOpacity>
                )}

                {option.type === 'action' &&
                  option.title === 'Delete Account' && (
                    <TouchableOpacity onPress={option.action}>
                      <Text style={styles.deleteText}>Delete</Text>
                    </TouchableOpacity>
                  )}
              </View>
            ))}
          </View>
        ))}

        <View style={styles.versionContainer}>
          <Text
            style={[
              styles.versionText,
              { color: isDark ? '#BBBBBB' : '#757575' },
            ]}
          >
            BarterMe v1.0.0
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 50,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 44,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    marginTop: 16,
    borderRadius: 8,
    marginHorizontal: 16,
    overflow: 'hidden',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 16,
    paddingVertical: 8,
    textTransform: 'uppercase',
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 14,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  optionIcon: {
    marginRight: 12,
  },
  optionTitle: {
    fontSize: 16,
  },
  actionText: {
    color: '#2196F3',
    fontSize: 16,
  },
  logoutText: {
    color: '#FF9800',
    fontSize: 16,
    fontWeight: '500',
  },
  deleteText: {
    color: '#F44336',
    fontSize: 16,
    fontWeight: '500',
  },
  versionContainer: {
    alignItems: 'center',
    marginVertical: 24,
  },
  versionText: {
    fontSize: 14,
  },
});

export default SettingsScreen;
