import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type ChatScreenNavigationProp = NativeStackNavigationProp<RootStackParamList>;
type ChatScreenRouteProp = RouteProp<RootStackParamList, 'Chat'>;

interface Message {
  id: string;
  text: string;
  isSender: boolean;
  timestamp: string;
  isRead: boolean;
}

export function ChatScreen() {
  const navigation = useNavigation<ChatScreenNavigationProp>();
  const route = useRoute<ChatScreenRouteProp>();
  const { colors } = useTheme();
  const [input, setInput] = useState('');
  const [messages, setMessages] = useState<Message[]>([]);
  const flatListRef = useRef<FlatList>(null);

  // Placeholder data for the other user
  const otherUser = {
    id: route.params?.userId || 'user123',
    name: 'Sarah Miller',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    lastSeen: 'Online',
  };

  // Initialize with some sample messages
  useEffect(() => {
    setMessages([
      {
        id: '1',
        text: 'Hi there! I noticed your listing for the mechanical keyboard. Is it still available?',
        isSender: false,
        timestamp: '2023-09-15T10:30:00Z',
        isRead: true,
      },
      {
        id: '2',
        text: 'Yes, it is! Are you interested in trading?',
        isSender: true,
        timestamp: '2023-09-15T10:32:00Z',
        isRead: true,
      },
      {
        id: '3',
        text: "Definitely! I have a pair of Sony headphones that I could trade. They're almost new and in great condition.",
        isSender: false,
        timestamp: '2023-09-15T10:35:00Z',
        isRead: true,
      },
      {
        id: '4',
        text: 'That sounds interesting! Could you send me some pictures of the headphones?',
        isSender: true,
        timestamp: '2023-09-15T10:37:00Z',
        isRead: true,
      },
      {
        id: '5',
        text: 'Sure! Here they are. What do you think?',
        isSender: false,
        timestamp: '2023-09-15T10:40:00Z',
        isRead: true,
      },
      {
        id: '6',
        text: "I could throw in a carrying case for the keyboard if you're interested.",
        isSender: false,
        timestamp: 'Just now',
        isRead: false,
      },
    ]);
  }, []);

  useEffect(() => {
    // Scroll to bottom when messages change
    if (flatListRef.current) {
      flatListRef.current.scrollToEnd({ animated: true });
    }
  }, [messages]);

  const handleSend = () => {
    if (input.trim() === '') return;

    const newMessage: Message = {
      id: Date.now().toString(),
      text: input.trim(),
      isSender: true,
      timestamp: 'Just now',
      isRead: false,
    };

    setMessages([...messages, newMessage]);
    setInput('');
  };

  const formatTime = (timestamp: string) => {
    if (timestamp === 'Just now') return timestamp;

    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleUserPress = () => {
    navigation.navigate('Profile', { userId: otherUser.id });
  };

  const handleTradePress = () => {
    navigation.navigate('TradeOffer', { userId: otherUser.id });
  };

  return (
    <KeyboardAvoidingView
      style={[styles.container, { backgroundColor: colors.background }]}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.userInfo} onPress={handleUserPress}>
          <Image source={{ uri: otherUser.avatar }} style={styles.avatar} />
          <View style={styles.userTextContainer}>
            <Text style={[styles.userName, { color: colors.text }]}>
              {otherUser.name}
            </Text>
            <Text style={[styles.userStatus, { color: colors.text + '99' }]}>
              {otherUser.lastSeen}
            </Text>
          </View>
        </TouchableOpacity>

        <TouchableOpacity style={styles.tradeButton} onPress={handleTradePress}>
          <Ionicons name="swap-horizontal" size={24} color={colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.messagesContainer}
        showsVerticalScrollIndicator={false}
        renderItem={({ item, index }) => {
          const showTimestamp =
            index === 0 ||
            new Date(item.timestamp).getDate() !==
              new Date(messages[index - 1].timestamp).getDate();

          const isFirstInGroup =
            index === 0 ||
            item.isSender !== messages[index - 1].isSender ||
            new Date(item.timestamp).getTime() -
              new Date(messages[index - 1].timestamp).getTime() >
              5 * 60 * 1000;

          const isLastInGroup =
            index === messages.length - 1 ||
            item.isSender !== messages[index + 1].isSender ||
            new Date(messages[index + 1].timestamp).getTime() -
              new Date(item.timestamp).getTime() >
              5 * 60 * 1000;

          return (
            <View>
              {showTimestamp && (
                <View style={styles.dateContainer}>
                  <Text
                    style={[styles.dateText, { color: colors.text + '80' }]}
                  >
                    {new Date(item.timestamp).toLocaleDateString([], {
                      weekday: 'long',
                      month: 'short',
                      day: 'numeric',
                    })}
                  </Text>
                </View>
              )}

              <View
                style={[
                  styles.messageRow,
                  item.isSender ? styles.sentRow : styles.receivedRow,
                ]}
              >
                {!item.isSender && isFirstInGroup && (
                  <Image
                    source={{ uri: otherUser.avatar }}
                    style={styles.messageAvatar}
                  />
                )}

                {!item.isSender && !isFirstInGroup && (
                  <View style={styles.messagePlaceholder} />
                )}

                <View
                  style={[
                    styles.messageBubble,
                    item.isSender
                      ? [styles.sentBubble, { backgroundColor: colors.primary }]
                      : [
                          styles.receivedBubble,
                          { backgroundColor: colors.card },
                        ],
                    isFirstInGroup &&
                      (item.isSender ? styles.sentFirst : styles.receivedFirst),
                    isLastInGroup &&
                      (item.isSender ? styles.sentLast : styles.receivedLast),
                  ]}
                >
                  <Text
                    style={[
                      styles.messageText,
                      { color: item.isSender ? '#FFFFFF' : colors.text },
                    ]}
                  >
                    {item.text}
                  </Text>
                  <Text
                    style={[
                      styles.timestamp,
                      {
                        color: item.isSender ? '#FFFFFF99' : colors.text + '80',
                      },
                    ]}
                  >
                    {formatTime(item.timestamp)}
                    {item.isSender && (
                      <Ionicons
                        name={item.isRead ? 'checkmark-done' : 'checkmark'}
                        size={14}
                        color={item.isRead ? '#FFFFFF99' : '#FFFFFF80'}
                        style={{ marginLeft: 4 }}
                      />
                    )}
                  </Text>
                </View>
              </View>
            </View>
          );
        }}
      />

      {/* Input Area */}
      <View style={[styles.inputContainer, { backgroundColor: colors.card }]}>
        <View
          style={[
            styles.inputWrapper,
            { backgroundColor: colors.border, borderColor: colors.border },
          ]}
        >
          <TouchableOpacity style={styles.attachButton}>
            <Ionicons name="add-circle-outline" size={24} color={colors.text} />
          </TouchableOpacity>

          <TextInput
            style={[styles.input, { color: colors.text }]}
            placeholder="Type a message..."
            placeholderTextColor={colors.text + '80'}
            value={input}
            onChangeText={setInput}
            multiline
          />

          <TouchableOpacity>
            <Ionicons name="camera-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        <TouchableOpacity
          style={[styles.sendButton, { backgroundColor: colors.primary }]}
          onPress={handleSend}
        >
          <Ionicons name="send" size={20} color="#FFFFFF" />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingTop: 60,
    paddingBottom: 10,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  userInfo: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    paddingLeft: 8,
  },
  avatar: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  userTextContainer: {
    marginLeft: 10,
  },
  userName: {
    fontWeight: '600',
    fontSize: 16,
  },
  userStatus: {
    fontSize: 12,
  },
  tradeButton: {
    padding: 8,
  },
  messagesContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  messageRow: {
    marginVertical: 2,
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  sentRow: {
    justifyContent: 'flex-end',
  },
  receivedRow: {
    justifyContent: 'flex-start',
  },
  messageAvatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    marginRight: 8,
  },
  messagePlaceholder: {
    width: 28,
    marginRight: 8,
  },
  messageBubble: {
    maxWidth: '75%',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 18,
  },
  sentBubble: {
    borderBottomRightRadius: 4,
  },
  receivedBubble: {
    borderBottomLeftRadius: 4,
  },
  sentFirst: {
    borderTopRightRadius: 18,
  },
  sentLast: {
    borderBottomRightRadius: 18,
  },
  receivedFirst: {
    borderTopLeftRadius: 18,
  },
  receivedLast: {
    borderBottomLeftRadius: 18,
  },
  messageText: {
    fontSize: 16,
    lineHeight: 22,
  },
  timestamp: {
    fontSize: 11,
    alignSelf: 'flex-end',
    marginTop: 4,
  },
  dateContainer: {
    alignItems: 'center',
    marginVertical: 16,
  },
  dateText: {
    fontSize: 12,
    fontWeight: '500',
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderTopColor: '#E0E0E0',
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F5F5F5',
    borderRadius: 24,
    paddingHorizontal: 12,
    borderWidth: 1,
  },
  input: {
    flex: 1,
    fontSize: 16,
    paddingVertical: 8,
    maxHeight: 100,
  },
  attachButton: {
    paddingRight: 8,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginLeft: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default ChatScreen;
