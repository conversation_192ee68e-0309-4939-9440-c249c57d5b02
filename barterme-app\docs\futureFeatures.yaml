---

## 💡 Future Ideas

- **AI trade fairness meter** using item history + pricing APIs
- **Geolocation-based matches** (swap local items)
- **Story-style item highlights**
- **NFT or digital collectible trades**
- **Community-built challenges** ("Best Sneaker Trade of the Week!")

---

## 🛠️ Dev Notes

- Use `@react-navigation` v6 with custom shared transitions
- Animate swipe gestures with `react-native-reanimated` v3 and `react-native-gesture-handler`
- Setup realtime with Supabase `channels` for trade updates and messages
- Store images in Supabase bucket with CDN enabled
- Build item schemas and chat metadata in PostgreSQL with relationships
- Integrate Stripe payment processing via React Native Stripe SDK
- Protect private trades with RLS (Row-Level Security) in Supabase

---

## 🧾 License & Contributions

This project is open-source and contributions are welcome. Please open issues, submit pull requests, and help build the future of bartering.

> "Trade it. Don't trash it." – BarterMe 🌀

---

