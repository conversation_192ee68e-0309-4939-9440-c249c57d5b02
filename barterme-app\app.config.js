import 'dotenv/config';

export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  slug: 'barterme',
  version: '1.0.0',
  orientation: 'portrait',
  icon: './assets/icon.png',
  splash: {
    image: './assets/splash.png',
    resizeMode: 'contain',
    backgroundColor: '#4A6FFF',
  },
  updates: {
    fallbackToCacheTimeout: 0,
  },
  assetBundlePatterns: ['**/*'],
  ios: {
    supportsTablet: true,
    bundleIdentifier: 'com.barterme.app',
  },
  android: {
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#4A6FFF',
    },
    package: 'com.barterme.app',
  },
  web: {
    favicon: './assets/favicon.png',
  },
  extra: {
    // Securely load variables from .env file
    supabaseUrl: process.env.SUPABASE_URL,
    supabaseAnonKey: process.env.SUPABASE_ANON_KEY,
  },
};
