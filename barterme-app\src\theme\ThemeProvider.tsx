import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from 'react';
import { useColorScheme } from 'react-native';

// Define theme colors type
interface ThemeColors {
  primary: string;
  background: string;
  card: string;
  text: string;
  border: string;
  notification: string;
  buttonText: string;
  tabBarBackground: string;
}

// Define theme context type
interface ThemeContextType {
  isDark: boolean;
  toggleTheme: () => void;
  theme: ThemeColors;
  colors: ThemeColors;
}

// Define theme provider props
interface ThemeProviderProps {
  children: ReactNode;
}

// Theme colors for light and dark modes
const themes: Record<'light' | 'dark', ThemeColors> = {
  light: {
    primary: '#FF6B6B',
    background: '#FFFFFF',
    card: '#F5F5F5',
    text: '#121212',
    border: '#E1E1E1',
    notification: '#FF3B30',
    buttonText: '#FFFFFF',
    tabBarBackground: '#FFFFFF',
  },
  dark: {
    primary: '#FF6B6B',
    background: '#121212',
    card: '#1E1E1E',
    text: '#FFFFFF',
    border: '#2C2C2C',
    notification: '#FF453A',
    buttonText: '#FFFFFF',
    tabBarBackground: '#1E1E1E',
  },
};

// Create a theme context
const ThemeContext = createContext<ThemeContextType>({
  isDark: false,
  toggleTheme: () => {},
  theme: themes.light,
  colors: themes.light,
});

// ThemeProvider component
export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // Get device color scheme
  const deviceTheme = useColorScheme();

  // State to track if we're using dark mode
  const [isDark, setIsDark] = useState<boolean>(deviceTheme === 'dark');

  // Update theme when device theme changes
  useEffect(() => {
    setIsDark(deviceTheme === 'dark');
  }, [deviceTheme]);

  // Function to toggle theme manually
  const toggleTheme = (): void => {
    setIsDark(!isDark);
  };

  // Value object to provide to consumers
  const themeContext: ThemeContextType = {
    isDark,
    toggleTheme,
    theme: isDark ? themes.dark : themes.light,
    colors: isDark ? themes.dark : themes.light,
  };

  return (
    <ThemeContext.Provider value={themeContext}>
      {children}
    </ThemeContext.Provider>
  );
};

// Custom hook to use the theme context
export const useTheme = (): ThemeContextType => useContext(ThemeContext);
