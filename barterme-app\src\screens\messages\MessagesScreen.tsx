import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type MessagesScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;

interface MessagePreview {
  id: string;
  userId: string;
  name: string;
  avatar: string;
  lastMessage: string;
  timestamp: string;
  unread: number;
}

export function MessagesScreen() {
  const navigation = useNavigation<MessagesScreenNavigationProp>();
  const { colors } = useTheme();

  // Placeholder data for message previews
  const [messages, setMessages] = useState<MessagePreview[]>([
    {
      id: '1',
      userId: 'user123',
      name: '<PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      lastMessage:
        "I could throw in a carrying case for the keyboard if you're interested.",
      timestamp: '2 min ago',
      unread: 1,
    },
    {
      id: '2',
      userId: 'user456',
      name: 'James Wilson',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      lastMessage: 'Would you consider trading for a vintage camera?',
      timestamp: '1 hour ago',
      unread: 0,
    },
    {
      id: '3',
      userId: 'user789',
      name: 'Emily Johnson',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
      lastMessage: 'Thanks for the quick reply!',
      timestamp: '2 hours ago',
      unread: 0,
    },
    {
      id: '4',
      userId: 'user101',
      name: 'Michael Brown',
      avatar: 'https://randomuser.me/api/portraits/men/91.jpg',
      lastMessage: 'Is the mountain bike still available?',
      timestamp: 'Yesterday',
      unread: 0,
    },
    {
      id: '5',
      userId: 'user102',
      name: 'Jessica Davis',
      avatar: 'https://randomuser.me/api/portraits/women/10.jpg',
      lastMessage: "Perfect! Let's meet tomorrow then.",
      timestamp: 'Yesterday',
      unread: 0,
    },
  ]);

  const handleChatPress = (item: MessagePreview) => {
    navigation.navigate('Chat', { userId: item.userId });
  };

  const renderItem = ({ item }: { item: MessagePreview }) => (
    <TouchableOpacity
      style={[styles.messageItem, { borderBottomColor: colors.border }]}
      onPress={() => handleChatPress(item)}
    >
      <Image source={{ uri: item.avatar }} style={styles.avatar} />

      <View style={styles.messageContent}>
        <View style={styles.messageHeader}>
          <Text style={[styles.name, { color: colors.text }]}>{item.name}</Text>
          <Text style={[styles.time, { color: colors.text + '99' }]}>
            {item.timestamp}
          </Text>
        </View>

        <View style={styles.messagePreview}>
          <Text
            style={[
              styles.previewText,
              { color: item.unread > 0 ? colors.text : colors.text + '99' },
              item.unread > 0 && styles.unreadText,
            ]}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {item.lastMessage}
          </Text>

          {item.unread > 0 && (
            <View
              style={[styles.unreadBadge, { backgroundColor: colors.primary }]}
            >
              <Text style={styles.unreadCount}>{item.unread}</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <Text style={[styles.title, { color: colors.text }]}>Messages</Text>
      </View>

      {messages.length > 0 ? (
        <FlatList
          data={messages}
          keyExtractor={(item) => item.id}
          renderItem={renderItem}
          contentContainerStyle={styles.list}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons
            name="chatbubble-ellipses-outline"
            size={60}
            color={colors.text + '40'}
          />
          <Text style={[styles.emptyText, { color: colors.text + '99' }]}>
            No messages yet
          </Text>
          <Text style={[styles.emptySubText, { color: colors.text + '80' }]}>
            Your conversations will appear here
          </Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingTop: 60,
    paddingBottom: 16,
    paddingHorizontal: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  list: {
    paddingBottom: 20,
  },
  messageItem: {
    flexDirection: 'row',
    padding: 16,
    borderBottomWidth: 1,
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    marginRight: 12,
  },
  messageContent: {
    flex: 1,
  },
  messageHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  name: {
    fontWeight: '600',
    fontSize: 16,
  },
  time: {
    fontSize: 12,
  },
  messagePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  previewText: {
    flex: 1,
    fontSize: 14,
    marginRight: 8,
  },
  unreadText: {
    fontWeight: '500',
  },
  unreadBadge: {
    minWidth: 20,
    height: 20,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 4,
  },
  unreadCount: {
    color: 'white',
    fontSize: 12,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    marginTop: 8,
    textAlign: 'center',
  },
});

export default MessagesScreen;
