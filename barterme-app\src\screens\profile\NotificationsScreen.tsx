import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../theme/ThemeProvider';

type NotificationsScreenNavigationProp = NativeStackNavigationProp<any>;

interface Notification {
  id: string;
  type: 'trade' | 'message' | 'system' | 'like';
  title: string;
  content: string;
  timestamp: string;
  read: boolean;
  data?: {
    tradeId?: string;
    userId?: string;
    itemId?: string;
  };
}

function NotificationsScreen() {
  const navigation = useNavigation<NotificationsScreenNavigationProp>();
  const { colors } = useTheme();

  // Notification preferences
  const [preferences, setPreferences] = useState({
    tradeUpdates: true,
    messages: true,
    appUpdates: false,
    activitySummary: true,
  });

  // Mock notifications data
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'trade',
      title: 'Trade Offer Accepted',
      content: '<PERSON> accepted your trade offer for Nintendo Switch.',
      timestamp: '10 min ago',
      read: false,
      data: {
        tradeId: 'trade123',
        userId: 'user456',
      },
    },
    {
      id: '2',
      type: 'message',
      title: 'New Message',
      content: 'Sarah Miller sent you a message about your headphones.',
      timestamp: '1 hour ago',
      read: false,
      data: {
        userId: 'user123',
      },
    },
    {
      id: '3',
      type: 'like',
      title: 'New Item Like',
      content: 'Jessica Davis liked your vintage camera.',
      timestamp: '3 hours ago',
      read: true,
      data: {
        itemId: 'item789',
        userId: 'user102',
      },
    },
    {
      id: '4',
      type: 'system',
      title: 'Welcome to BarterMe',
      content: 'Thanks for joining! Complete your profile to get started.',
      timestamp: 'Yesterday',
      read: true,
    },
    {
      id: '5',
      type: 'trade',
      title: 'New Trade Offer',
      content:
        'Emily Johnson wants to trade her mountain bike for your tablet.',
      timestamp: 'Yesterday',
      read: true,
      data: {
        tradeId: 'trade456',
        userId: 'user789',
      },
    },
  ]);

  const handleGoBack = () => {
    navigation.goBack();
  };

  const handleTogglePreference = (key: keyof typeof preferences) => {
    setPreferences({
      ...preferences,
      [key]: !preferences[key],
    });
  };

  const handleNotificationPress = (notification: Notification) => {
    // Mark as read
    const updatedNotifications = notifications.map((item) => {
      if (item.id === notification.id) {
        return { ...item, read: true };
      }
      return item;
    });
    setNotifications(updatedNotifications);

    // Navigate based on notification type
    if (notification.type === 'trade' && notification.data?.tradeId) {
      navigation.navigate('TradeDetails', {
        tradeId: notification.data.tradeId,
      });
    } else if (notification.type === 'message' && notification.data?.userId) {
      navigation.navigate('Chat', { userId: notification.data.userId });
    } else if (notification.type === 'like' && notification.data?.itemId) {
      navigation.navigate('ItemDetails', { itemId: notification.data.itemId });
    }
  };

  const handleMarkAllAsRead = () => {
    const updatedNotifications = notifications.map((item) => ({
      ...item,
      read: true,
    }));
    setNotifications(updatedNotifications);
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'trade':
        return 'swap-horizontal';
      case 'message':
        return 'chatbubble-ellipses';
      case 'system':
        return 'information-circle';
      case 'like':
        return 'heart';
      default:
        return 'notifications';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'trade':
        return '#4CAF50';
      case 'message':
        return '#2196F3';
      case 'system':
        return '#FF9800';
      case 'like':
        return '#F44336';
      default:
        return colors.primary;
    }
  };

  const renderItem = ({ item }: { item: Notification }) => (
    <TouchableOpacity
      style={[
        styles.notificationItem,
        {
          backgroundColor: item.read ? colors.card : colors.primary + '10',
          borderLeftColor: getNotificationColor(item.type),
        },
      ]}
      onPress={() => handleNotificationPress(item)}
    >
      <View
        style={[
          styles.iconContainer,
          { backgroundColor: getNotificationColor(item.type) + '20' },
        ]}
      >
        <Ionicons
          name={getNotificationIcon(item.type)}
          size={20}
          color={getNotificationColor(item.type)}
        />
      </View>

      <View style={styles.contentContainer}>
        <View style={styles.notificationHeader}>
          <Text
            style={[
              styles.notificationTitle,
              {
                color: colors.text,
                fontWeight: item.read ? '400' : '600',
              },
            ]}
          >
            {item.title}
          </Text>
          <Text style={[styles.timestamp, { color: colors.text + '80' }]}>
            {item.timestamp}
          </Text>
        </View>

        <Text
          style={[
            styles.notificationContent,
            { color: colors.text + (item.read ? '80' : '99') },
          ]}
          numberOfLines={2}
        >
          {item.content}
        </Text>
      </View>

      {!item.read && (
        <View style={[styles.unreadDot, { backgroundColor: colors.primary }]} />
      )}
    </TouchableOpacity>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Notifications
        </Text>

        {notifications.some((item) => !item.read) && (
          <TouchableOpacity
            onPress={handleMarkAllAsRead}
            style={styles.markReadButton}
          >
            <Text style={[styles.markReadText, { color: colors.primary }]}>
              Mark all read
            </Text>
          </TouchableOpacity>
        )}
      </View>

      {/* Content */}
      <FlatList
        data={notifications}
        keyExtractor={(item) => item.id}
        renderItem={renderItem}
        contentContainerStyle={styles.listContent}
        ListHeaderComponent={
          <View
            style={[
              styles.preferencesSection,
              { backgroundColor: colors.card },
            ]}
          >
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Notification Preferences
            </Text>

            <View style={styles.preferencesContainer}>
              {Object.entries(preferences).map(([key, value]) => (
                <View
                  key={key}
                  style={[
                    styles.preferenceItem,
                    { borderBottomColor: colors.border },
                  ]}
                >
                  <Text style={[styles.preferenceText, { color: colors.text }]}>
                    {key === 'tradeUpdates'
                      ? 'Trade Updates'
                      : key === 'messages'
                      ? 'Messages'
                      : key === 'appUpdates'
                      ? 'App Updates'
                      : 'Activity Summary'}
                  </Text>
                  <Switch
                    value={value}
                    onValueChange={() =>
                      handleTogglePreference(key as keyof typeof preferences)
                    }
                    trackColor={{
                      false: '#767577',
                      true: colors.primary + '80',
                    }}
                    thumbColor={value ? colors.primary : '#f4f3f4'}
                  />
                </View>
              ))}
            </View>
          </View>
        }
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons
              name="notifications-off-outline"
              size={60}
              color={colors.text + '40'}
            />
            <Text style={[styles.emptyText, { color: colors.text }]}>
              No notifications yet
            </Text>
            <Text style={[styles.emptySubText, { color: colors.text + '80' }]}>
              We'll notify you when something important happens
            </Text>
          </View>
        }
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    marginLeft: 8,
  },
  markReadButton: {
    padding: 8,
  },
  markReadText: {
    fontSize: 14,
    fontWeight: '500',
  },
  listContent: {
    paddingBottom: 20,
  },
  preferencesSection: {
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    padding: 16,
    paddingBottom: 8,
  },
  preferencesContainer: {
    paddingHorizontal: 16,
  },
  preferenceItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  preferenceText: {
    fontSize: 16,
  },
  notificationItem: {
    flexDirection: 'row',
    padding: 16,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
    borderLeftWidth: 3,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  contentContainer: {
    flex: 1,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  notificationTitle: {
    fontSize: 16,
    flex: 1,
  },
  timestamp: {
    fontSize: 12,
    marginLeft: 8,
  },
  notificationContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginLeft: 8,
    alignSelf: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginTop: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '500',
    marginTop: 16,
  },
  emptySubText: {
    fontSize: 14,
    textAlign: 'center',
    marginTop: 8,
    marginHorizontal: 32,
  },
});

export default NotificationsScreen;
