# 📱 BarterMe! – Swipe, Trade, Buy. Community-Powered Bartering

**BarterMe** is a next-gen mobile marketplace inspired by the swipe mechanics of Tinder—but for _items_ instead of people.

Users can **swipe left or right** on items they want to trade with, negotiate in real-time, and even **vote or react** to trades. Whether you're looking to **trade sneakers for speakers** or **buy that rare collectible**, BarterMe is your community-first platform for seamless, social bartering.

---

## 🚀 Tech Stack

| Layer              | Tech                                                                   |
| ------------------ | ---------------------------------------------------------------------- |
| Mobile Frontend    | [Expo](https://expo.dev), React Native, React Reanimated               |
| Backend            | Node.js, Express                                                       |
| Database           | [Supabase](https://supabase.com) (PostgreSQL, Auth, Realtime, Storage) |
| Realtime Chat      | Supabase Realtime / Socket.IO                                          |
| Payments           | PayPal SDK                                                             |
| Image Storage      | Supabase Storage                                                       |
| Push Notifications | Expo Notifications                                                     |

---

## 🔥 Core Features

### 🔄 Swipe to Trade

- Swipe left (👎) to pass
- Swipe right (🔥) to initiate a trade
- Users can add items to their trade offers or swap in cash via PayPal

### 👀 Public & Private Modes

- **Public Mode**: Trades are visible to all, with comments, emojis, and vote-for-winner mechanics
- **Private Mode**: One-to-one trading with no public visibility

### 💬 Messaging & Negotiations

- Realtime chat to discuss offers
- Modify offers dynamically (add/remove items or add money)
- Push notifications for trade requests, messages, likes, votes, and updates

### 🗳️ Trade Reactions & Voting

- Anyone can view public trades
- Add emoji reactions (🔥, 😂, 🤯, 💩, etc.)
- Vote on who "won" the trade

### 👤 User Profiles

- Custom avatars, bios, verified badge for trusted users
- Item inventory display (items available for trade)
- Trade history and reputation score

### 💵 Buy Option

- Add PayPal integration to allow outright purchases

---

## ✨ Animations and Transitions

BarterMe uses rich, modern animations to enhance UX:

- **Onboarding Screens**: Smooth slides with React Native Reanimated 3 + Gesture Handler
- **Swipe Cards**: Like Tinder – physics-based swipes
- **Modal Transitions**: Custom animated modals for offers and chat
- **Loading Screens**: Lottie Animations and shimmering placeholders

---

## 📲 App Flow

1. **Onboarding** – Choose username, interests, location, preferred trade categories
2. **Login / Signup** – Email/Password or OAuth (via Supabase)
3. **Swipe UI** – Tinder-style swipes with item info, photos, and ratings
4. **Offer Builder** – Dynamically add your items or offer cash to sweeten the deal
5. **Inbox / Chat** – Message traders and finalize negotiations
6. **Profile** – Manage your items, view your history, see trade stats
7. **Trade Feed (Public Mode)** – Browse others' public trades and cast your vote

---

## 🧱 Folder Structure (Suggested)
