import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type TradeDetailsScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;
type TradeDetailsScreenRouteProp = RouteProp<any, 'TradeDetails'>;

function TradeDetailsScreen() {
  const navigation = useNavigation<TradeDetailsScreenNavigationProp>();
  const route = useRoute<TradeDetailsScreenRouteProp>();
  const { colors } = useTheme();

  // Get the tradeId from route params or use a placeholder
  const tradeId = route.params?.tradeId || 'trade123';

  // Trade details - in a real app, would be fetched based on tradeId
  const [trade, setTrade] = useState({
    id: tradeId,
    status: 'pending', // 'pending', 'accepted', 'rejected', 'completed'
    date: 'May 15, 2023',
    lastUpdated: '2 days ago',
    otherUser: {
      id: 'user456',
      name: 'Michael Johnson',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      rating: 4.7,
    },
    myItem: {
      id: 'item123',
      name: 'Sony WH-1000XM4 Headphones',
      image:
        'https://images.unsplash.com/photo-1505751171710-1f6d0ace5a85?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      value: '$299',
      condition: 'Like New',
    },
    theirItem: {
      id: 'item456',
      name: 'Nintendo Switch',
      image:
        'https://images.unsplash.com/photo-1585857188898-7c3d2324837d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      value: '$279',
      condition: 'Good',
    },
    messages: [
      {
        id: 'msg1',
        content:
          "I'm interested in trading my Nintendo Switch for your headphones.",
        timestamp: 'May 15, 2023',
        sender: 'them',
      },
      {
        id: 'msg2',
        content: 'Sounds good! Are the controllers included?',
        timestamp: 'May 15, 2023',
        sender: 'me',
      },
      {
        id: 'msg3',
        content: 'Yes, comes with everything in the original box.',
        timestamp: 'May 16, 2023',
        sender: 'them',
      },
    ],
  });

  const handleGoBack = () => {
    navigation.goBack();
  };

  const handleViewItem = (itemId: string) => {
    navigation.navigate('ItemDetails', { itemId });
  };

  const handleViewProfile = () => {
    navigation.navigate('Profile', { userId: trade.otherUser.id });
  };

  const handleMessage = () => {
    navigation.navigate('Chat', {
      tradeId: trade.id,
      userId: trade.otherUser.id,
    });
  };

  const handleAcceptTrade = () => {
    Alert.alert('Accept Trade', 'Are you sure you want to accept this trade?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Accept',
        onPress: () => {
          setTrade({ ...trade, status: 'accepted' });
          // In a real app, would make API call to accept trade
        },
      },
    ]);
  };

  const handleRejectTrade = () => {
    Alert.alert('Reject Trade', 'Are you sure you want to reject this trade?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Reject',
        style: 'destructive',
        onPress: () => {
          setTrade({ ...trade, status: 'rejected' });
          // In a real app, would make API call to reject trade
        },
      },
    ]);
  };

  const handleCompleteTrade = () => {
    Alert.alert(
      'Complete Trade',
      'Confirm that you have exchanged items and want to complete this trade?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Complete',
          onPress: () => {
            setTrade({ ...trade, status: 'completed' });
            // In a real app, would make API call to complete trade
          },
        },
      ]
    );
  };

  const getStatusColor = () => {
    switch (trade.status) {
      case 'accepted':
        return '#4CAF50';
      case 'rejected':
        return '#F44336';
      case 'completed':
        return '#2196F3';
      default:
        return '#FFC107';
    }
  };

  const getStatusText = () => {
    switch (trade.status) {
      case 'accepted':
        return 'Accepted';
      case 'rejected':
        return 'Rejected';
      case 'completed':
        return 'Completed';
      default:
        return 'Pending';
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity onPress={handleGoBack} style={styles.backButton}>
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Trade Details
        </Text>
        <View style={styles.headerPlaceholder} />
      </View>

      <ScrollView style={styles.scrollView}>
        {/* Status and Date */}
        <View
          style={[styles.statusContainer, { backgroundColor: colors.card }]}
        >
          <View style={styles.statusRow}>
            <View
              style={[
                styles.statusBadge,
                {
                  backgroundColor: getStatusColor() + '20',
                  borderColor: getStatusColor(),
                },
              ]}
            >
              <Text style={[styles.statusText, { color: getStatusColor() }]}>
                {getStatusText()}
              </Text>
            </View>
            <Text style={[styles.dateText, { color: colors.text + '80' }]}>
              Created on {trade.date}
            </Text>
          </View>
          <Text style={[styles.lastUpdatedText, { color: colors.text + '60' }]}>
            Last updated {trade.lastUpdated}
          </Text>
        </View>

        {/* Trading with */}
        <View style={[styles.userSection, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Trading with
          </Text>
          <TouchableOpacity style={styles.userRow} onPress={handleViewProfile}>
            <Image
              source={{ uri: trade.otherUser.avatar }}
              style={styles.avatar}
            />
            <View style={styles.userInfo}>
              <Text style={[styles.userName, { color: colors.text }]}>
                {trade.otherUser.name}
              </Text>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={14} color="#FFD700" />
                <Text
                  style={[styles.ratingText, { color: colors.text + '80' }]}
                >
                  {trade.otherUser.rating}
                </Text>
              </View>
            </View>
            <Ionicons
              name="chevron-forward"
              size={20}
              color={colors.text + '60'}
            />
          </TouchableOpacity>
        </View>

        {/* Trade Items */}
        <View style={[styles.itemsSection, { backgroundColor: colors.card }]}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Trade Items
          </Text>

          {/* My Item */}
          <View style={styles.itemContainer}>
            <Text style={[styles.itemOwnerText, { color: colors.text + '80' }]}>
              Your item
            </Text>
            <TouchableOpacity
              style={[styles.itemCard, { borderColor: colors.border }]}
              onPress={() => handleViewItem(trade.myItem.id)}
            >
              <Image
                source={{ uri: trade.myItem.image }}
                style={styles.itemImage}
              />
              <View style={styles.itemInfo}>
                <Text style={[styles.itemName, { color: colors.text }]}>
                  {trade.myItem.name}
                </Text>
                <Text
                  style={[styles.itemDetail, { color: colors.text + '80' }]}
                >
                  Value: {trade.myItem.value}
                </Text>
                <Text
                  style={[styles.itemDetail, { color: colors.text + '80' }]}
                >
                  Condition: {trade.myItem.condition}
                </Text>
              </View>
            </TouchableOpacity>
          </View>

          {/* Direction Icon */}
          <View style={styles.directionContainer}>
            <Ionicons name="swap-vertical" size={24} color={colors.primary} />
          </View>

          {/* Their Item */}
          <View style={styles.itemContainer}>
            <Text style={[styles.itemOwnerText, { color: colors.text + '80' }]}>
              Their item
            </Text>
            <TouchableOpacity
              style={[styles.itemCard, { borderColor: colors.border }]}
              onPress={() => handleViewItem(trade.theirItem.id)}
            >
              <Image
                source={{ uri: trade.theirItem.image }}
                style={styles.itemImage}
              />
              <View style={styles.itemInfo}>
                <Text style={[styles.itemName, { color: colors.text }]}>
                  {trade.theirItem.name}
                </Text>
                <Text
                  style={[styles.itemDetail, { color: colors.text + '80' }]}
                >
                  Value: {trade.theirItem.value}
                </Text>
                <Text
                  style={[styles.itemDetail, { color: colors.text + '80' }]}
                >
                  Condition: {trade.theirItem.condition}
                </Text>
              </View>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Messages */}
        <View
          style={[styles.messagesSection, { backgroundColor: colors.card }]}
        >
          <View style={styles.messageHeader}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Recent Messages
            </Text>
            <TouchableOpacity
              onPress={handleMessage}
              style={styles.viewAllButton}
            >
              <Text style={[styles.viewAllText, { color: colors.primary }]}>
                View All
              </Text>
            </TouchableOpacity>
          </View>

          {trade.messages.map((message) => (
            <View
              key={message.id}
              style={[
                styles.messageItem,
                {
                  backgroundColor:
                    message.sender === 'me'
                      ? colors.primary + '15'
                      : colors.card,
                  alignSelf:
                    message.sender === 'me' ? 'flex-end' : 'flex-start',
                },
              ]}
            >
              <Text style={[styles.messageContent, { color: colors.text }]}>
                {message.content}
              </Text>
              <Text style={[styles.messageTime, { color: colors.text + '60' }]}>
                {message.timestamp}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>

      {/* Actions Footer */}
      {trade.status === 'pending' && (
        <View
          style={[
            styles.footer,
            { backgroundColor: colors.card, borderTopColor: colors.border },
          ]}
        >
          <TouchableOpacity
            style={[styles.rejectButton, { borderColor: '#F44336' }]}
            onPress={handleRejectTrade}
          >
            <Text style={styles.rejectButtonText}>Reject</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.acceptButton, { backgroundColor: '#4CAF50' }]}
            onPress={handleAcceptTrade}
          >
            <Text style={styles.acceptButtonText}>Accept</Text>
          </TouchableOpacity>
        </View>
      )}

      {trade.status === 'accepted' && (
        <View
          style={[
            styles.footer,
            { backgroundColor: colors.card, borderTopColor: colors.border },
          ]}
        >
          <TouchableOpacity
            style={[styles.acceptButton, { backgroundColor: '#2196F3' }]}
            onPress={handleCompleteTrade}
          >
            <Text style={styles.acceptButtonText}>Complete Trade</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerPlaceholder: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  statusContainer: {
    margin: 16,
    padding: 16,
    borderRadius: 12,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    borderWidth: 1,
  },
  statusText: {
    fontWeight: '600',
    fontSize: 14,
  },
  dateText: {
    fontSize: 14,
  },
  lastUpdatedText: {
    fontSize: 12,
    marginTop: 8,
  },
  userSection: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  userRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  userInfo: {
    flex: 1,
    marginLeft: 12,
  },
  userName: {
    fontSize: 16,
    fontWeight: '500',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  ratingText: {
    fontSize: 14,
    marginLeft: 4,
  },
  itemsSection: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
  },
  itemContainer: {
    marginBottom: 12,
  },
  itemOwnerText: {
    fontSize: 14,
    marginBottom: 8,
  },
  itemCard: {
    flexDirection: 'row',
    borderRadius: 8,
    borderWidth: 1,
    overflow: 'hidden',
  },
  itemImage: {
    width: 100,
    height: 100,
  },
  itemInfo: {
    flex: 1,
    padding: 12,
    justifyContent: 'center',
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemDetail: {
    fontSize: 14,
    marginBottom: 2,
  },
  directionContainer: {
    alignItems: 'center',
    marginVertical: 12,
  },
  messagesSection: {
    margin: 16,
    marginTop: 0,
    padding: 16,
    borderRadius: 12,
    marginBottom: 24,
  },
  messageHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  viewAllButton: {
    padding: 4,
  },
  viewAllText: {
    fontSize: 14,
  },
  messageItem: {
    padding: 12,
    borderRadius: 8,
    maxWidth: '80%',
    marginBottom: 8,
  },
  messageContent: {
    fontSize: 14,
    lineHeight: 20,
  },
  messageTime: {
    fontSize: 12,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  footer: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
  },
  rejectButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
    marginRight: 8,
  },
  rejectButtonText: {
    color: '#F44336',
    fontSize: 16,
    fontWeight: '500',
  },
  acceptButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginLeft: 8,
  },
  acceptButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '500',
  },
});

export default TradeDetailsScreen;
