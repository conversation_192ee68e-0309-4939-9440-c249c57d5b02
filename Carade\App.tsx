/**
 * Carade App
 * Humble Chaudhry
 *
 * @format
 */
import React from 'react';
import {View, StyleSheet} from 'react-native';

import users from './assets/data/users';

import Card from './src/components/TinderCard';
import AnimatedStack from './src/components/AnimatedStack';

const App = () => {
  return (
    <View style={styles.root}>
      <AnimatedStack
        data={users}
        renderItem={({item}) => <Card user={item} />}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  root: {
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
    width: '100%',
  },
});
export default App;
