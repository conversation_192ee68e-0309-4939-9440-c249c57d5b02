import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  Alert,
} from 'react-native';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type TradeOfferScreenRouteProp = RouteProp<RootStackParamList, 'TradeOffer'>;
type TradeOfferScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;

// Placeholder trade data
const TRADE = {
  id: '1',
  status: 'pending',
  date: '2023-09-15',
  createdAt: '2023-09-15T14:30:00Z',
  yourItem: {
    id: '101',
    title: 'Vintage Camera',
    description:
      'A beautiful vintage film camera from the 1970s. In excellent working condition with minor cosmetic wear.',
    condition: 'Excellent',
    estimatedValue: '$120',
    image:
      'https://images.unsplash.com/photo-1516035069371-29a1b244cc32?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
  },
  theirItem: {
    id: '201',
    title: 'Mechanical Keyboard',
    description:
      'Custom mechanical keyboard with Cherry MX Brown switches and PBT keycaps. Includes USB-C cable.',
    condition: 'Like New',
    estimatedValue: '$150',
    image:
      'https://images.unsplash.com/photo-1595225476474-57a67f26cf5d?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
  },
  user: {
    id: 'user456',
    name: 'Sarah Miller',
    rating: 4.8,
    totalTrades: 23,
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
  },
  messages: [
    {
      id: 'm1',
      text: "Hi, I'm interested in trading my mechanical keyboard for your vintage camera. What do you think?",
      sender: 'user456',
      timestamp: '2023-09-15T14:30:00Z',
    },
    {
      id: 'm2',
      text: "I could throw in a carrying case for the keyboard if you're interested.",
      sender: 'user456',
      timestamp: '2023-09-15T14:32:00Z',
    },
  ],
};

export const TradeOfferScreen: React.FC = () => {
  const route = useRoute<TradeOfferScreenRouteProp>();
  const navigation = useNavigation<TradeOfferScreenNavigationProp>();
  const { colors } = useTheme();
  const [tradeStatus, setTradeStatus] = useState(TRADE.status);

  // In a real app, we would fetch the trade data using the itemId from the route params
  // const { itemId } = route.params;

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const handleAcceptTrade = () => {
    Alert.alert(
      'Accept Trade',
      'Are you sure you want to accept this trade offer?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Accept',
          onPress: () => {
            setTradeStatus('accepted');
            // In a real app, we would call an API to update the trade status
          },
        },
      ]
    );
  };

  const handleDeclineTrade = () => {
    Alert.alert(
      'Decline Trade',
      'Are you sure you want to decline this trade offer?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Decline',
          onPress: () => {
            setTradeStatus('declined');
            // In a real app, we would call an API to update the trade status
          },
        },
      ]
    );
  };

  const handleChat = () => {
    navigation.navigate('Chat', { userId: TRADE.user.id });
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={[styles.header, { backgroundColor: colors.card }]}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Ionicons name="arrow-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Trade Offer
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Trade Status Badge */}
        <View style={styles.statusContainer}>
          <View
            style={[
              styles.statusBadge,
              {
                backgroundColor:
                  tradeStatus === 'pending'
                    ? '#F9A82520'
                    : tradeStatus === 'accepted'
                    ? '#4CAF5020'
                    : '#F4433620',
              },
            ]}
          >
            <Text
              style={[
                styles.statusText,
                {
                  color:
                    tradeStatus === 'pending'
                      ? '#F9A825'
                      : tradeStatus === 'accepted'
                      ? '#4CAF50'
                      : '#F44336',
                },
              ]}
            >
              {tradeStatus === 'pending'
                ? 'Pending'
                : tradeStatus === 'accepted'
                ? 'Accepted'
                : 'Declined'}
            </Text>
          </View>
          <Text style={[styles.dateText, { color: colors.text + '80' }]}>
            Offer received on {formatDate(TRADE.createdAt)}
          </Text>
        </View>

        {/* User Info */}
        <View style={[styles.userCard, { backgroundColor: colors.card }]}>
          <Image source={{ uri: TRADE.user.avatar }} style={styles.avatar} />
          <View style={styles.userInfo}>
            <Text style={[styles.userName, { color: colors.text }]}>
              {TRADE.user.name}
            </Text>
            <View style={styles.ratingContainer}>
              <Ionicons name="star" size={16} color="#FFD700" />
              <Text style={[styles.ratingText, { color: colors.text + 'CC' }]}>
                {TRADE.user.rating} · {TRADE.user.totalTrades} trades
              </Text>
            </View>
          </View>
        </View>

        {/* Items being traded */}
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Trade Details
        </Text>

        <View style={[styles.itemsContainer, { backgroundColor: colors.card }]}>
          <View style={styles.itemSection}>
            <Text
              style={[styles.itemOwnerLabel, { color: colors.text + '80' }]}
            >
              Their Item
            </Text>
            <Image
              source={{ uri: TRADE.theirItem.image }}
              style={styles.itemImage}
            />
            <Text style={[styles.itemTitle, { color: colors.text }]}>
              {TRADE.theirItem.title}
            </Text>
            <Text style={[styles.itemValue, { color: colors.primary }]}>
              {TRADE.theirItem.estimatedValue}
            </Text>
            <Text style={[styles.itemCondition, { color: colors.text + '80' }]}>
              Condition: {TRADE.theirItem.condition}
            </Text>
            <Text
              style={[styles.itemDescription, { color: colors.text + 'CC' }]}
            >
              {TRADE.theirItem.description}
            </Text>
          </View>

          <View style={styles.divider} />

          <View style={styles.itemSection}>
            <Text
              style={[styles.itemOwnerLabel, { color: colors.text + '80' }]}
            >
              Your Item
            </Text>
            <Image
              source={{ uri: TRADE.yourItem.image }}
              style={styles.itemImage}
            />
            <Text style={[styles.itemTitle, { color: colors.text }]}>
              {TRADE.yourItem.title}
            </Text>
            <Text style={[styles.itemValue, { color: colors.primary }]}>
              {TRADE.yourItem.estimatedValue}
            </Text>
            <Text style={[styles.itemCondition, { color: colors.text + '80' }]}>
              Condition: {TRADE.yourItem.condition}
            </Text>
            <Text
              style={[styles.itemDescription, { color: colors.text + 'CC' }]}
            >
              {TRADE.yourItem.description}
            </Text>
          </View>
        </View>

        {/* Messages */}
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          Messages
        </Text>

        <View
          style={[styles.messagesContainer, { backgroundColor: colors.card }]}
        >
          {TRADE.messages.map((message) => (
            <View key={message.id} style={styles.messageItem}>
              <Text style={[styles.messageText, { color: colors.text }]}>
                {message.text}
              </Text>
              <Text style={[styles.messageTime, { color: colors.text + '80' }]}>
                {new Date(message.timestamp).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </Text>
            </View>
          ))}
          <TouchableOpacity
            style={[styles.viewAllMessages, { borderTopColor: colors.border }]}
            onPress={handleChat}
          >
            <Text
              style={[styles.viewAllMessagesText, { color: colors.primary }]}
            >
              View all messages
            </Text>
            <Ionicons name="chevron-forward" size={16} color={colors.primary} />
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Action buttons for pending trades */}
      {tradeStatus === 'pending' && (
        <View style={[styles.footer, { backgroundColor: colors.background }]}>
          <TouchableOpacity
            style={[
              styles.declineButton,
              { backgroundColor: colors.card, borderColor: '#F44336' },
            ]}
            onPress={handleDeclineTrade}
          >
            <Text style={[styles.declineButtonText, { color: '#F44336' }]}>
              Decline
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.acceptButton, { backgroundColor: colors.primary }]}
            onPress={handleAcceptTrade}
          >
            <Text
              style={[styles.acceptButtonText, { color: colors.buttonText }]}
            >
              Accept Trade
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Info for accepted trades */}
      {tradeStatus === 'accepted' && (
        <View style={[styles.acceptedFooter, { backgroundColor: colors.card }]}>
          <View style={styles.acceptedIconContainer}>
            <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
          </View>
          <Text style={[styles.acceptedText, { color: colors.text }]}>
            Trade accepted! Contact the other trader to arrange exchange
            details.
          </Text>
          <TouchableOpacity
            style={[styles.contactButton, { backgroundColor: colors.primary }]}
            onPress={handleChat}
          >
            <Text
              style={[styles.contactButtonText, { color: colors.buttonText }]}
            >
              Contact Trader
            </Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  backButton: {
    padding: 8,
  },
  headerRight: {
    width: 40,
  },
  scrollView: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100,
  },
  statusContainer: {
    alignItems: 'center',
    padding: 20,
  },
  statusBadge: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
    marginBottom: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: 'bold',
  },
  dateText: {
    fontSize: 14,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
    marginHorizontal: 20,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  avatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  userInfo: {
    marginLeft: 16,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 2,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    marginLeft: 4,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginHorizontal: 20,
    marginBottom: 12,
    marginTop: 16,
  },
  itemsContainer: {
    marginHorizontal: 20,
    borderRadius: 12,
    overflow: 'hidden',
  },
  itemSection: {
    padding: 16,
  },
  itemOwnerLabel: {
    fontSize: 14,
    marginBottom: 12,
    fontWeight: '500',
  },
  itemImage: {
    width: '100%',
    height: 180,
    borderRadius: 8,
    marginBottom: 16,
  },
  itemTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  itemValue: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  itemCondition: {
    fontSize: 14,
    marginBottom: 8,
  },
  itemDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  divider: {
    height: 1,
    backgroundColor: '#E0E0E0',
    opacity: 0.5,
  },
  messagesContainer: {
    marginHorizontal: 20,
    borderRadius: 12,
    overflow: 'hidden',
    marginBottom: 20,
  },
  messageItem: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E030',
  },
  messageText: {
    fontSize: 15,
    marginBottom: 8,
    lineHeight: 20,
  },
  messageTime: {
    fontSize: 12,
    alignSelf: 'flex-end',
  },
  viewAllMessages: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderTopWidth: 1,
  },
  viewAllMessagesText: {
    fontSize: 14,
    fontWeight: '500',
    marginRight: 4,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E020',
  },
  declineButton: {
    flex: 1,
    marginRight: 10,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
  },
  declineButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  acceptButton: {
    flex: 2,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  acceptButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  acceptedFooter: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
    alignItems: 'center',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
  },
  acceptedIconContainer: {
    marginBottom: 8,
  },
  acceptedText: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: 12,
    lineHeight: 20,
  },
  contactButton: {
    width: '100%',
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});

export default TradeOfferScreen;
