import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  ScrollView,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type ChatDetailsScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;
type ChatDetailsScreenRouteProp = RouteProp<RootStackParamList, 'ChatDetails'>;

interface SharedItem {
  id: string;
  name: string;
  image: string;
  tradeStatus?: 'pending' | 'completed' | 'rejected';
}

export function ChatDetailsScreen() {
  const navigation = useNavigation<ChatDetailsScreenNavigationProp>();
  const route = useRoute<ChatDetailsScreenRouteProp>();
  const { colors } = useTheme();

  // Get the userId from route params or use a placeholder
  const userId = route.params?.userId || 'user123';

  // User details - in a real app, would be fetched based on userId
  const [user, setUser] = useState({
    id: userId,
    name: 'Sarah Miller',
    username: '@sarahmiller',
    avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
    bio: 'Tech enthusiast and vintage collector. Love trading for unique items!',
    location: 'San Francisco, CA',
    rating: 4.8,
    totalRatings: 24,
    memberSince: 'March 2022',
    blocked: false,
  });

  // Shared items between users - in a real app, would be fetched from an API
  const [sharedItems, setSharedItems] = useState<SharedItem[]>([
    {
      id: '1',
      name: 'Mechanical Keyboard',
      image:
        'https://images.unsplash.com/photo-1595225476474-88951441ce78?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      tradeStatus: 'pending',
    },
    {
      id: '2',
      name: 'Sony Headphones',
      image:
        'https://images.unsplash.com/photo-1505751171710-1f6d0ace5a85?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
    },
    {
      id: '3',
      name: 'Vintage Camera',
      image:
        'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1050&q=80',
      tradeStatus: 'completed',
    },
  ]);

  const handleBackPress = () => {
    navigation.goBack();
  };

  const handleChatPress = () => {
    navigation.navigate('Chat', { userId: user.id });
  };

  const handleProfilePress = () => {
    navigation.navigate('Profile', { userId: user.id });
  };

  const handleBlockUser = () => {
    setUser({ ...user, blocked: !user.blocked });
    // In a real app, would make an API call to block/unblock user
  };

  const handleItemPress = (item: SharedItem) => {
    navigation.navigate('ItemDetails', { itemId: item.id });
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={[styles.header, { backgroundColor: colors.background }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBackPress}>
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Chat Details
        </Text>
        <View style={styles.headerRight} />
      </View>

      <ScrollView>
        {/* User Profile */}
        <View style={[styles.profileSection, { backgroundColor: colors.card }]}>
          <Image source={{ uri: user.avatar }} style={styles.avatar} />
          <Text style={[styles.name, { color: colors.text }]}>{user.name}</Text>
          <Text style={[styles.username, { color: colors.text + '80' }]}>
            {user.username}
          </Text>

          <View style={styles.ratingContainer}>
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <Ionicons
                  key={star}
                  name={
                    star <= Math.floor(user.rating)
                      ? 'star'
                      : star <= user.rating
                      ? 'star-half'
                      : 'star-outline'
                  }
                  size={16}
                  color="#FFD700"
                  style={{ marginHorizontal: 1 }}
                />
              ))}
            </View>
            <Text style={[styles.ratingText, { color: colors.text + '80' }]}>
              {user.rating} ({user.totalRatings} ratings)
            </Text>
          </View>

          <Text style={[styles.bio, { color: colors.text }]}>{user.bio}</Text>

          <View style={styles.infoRow}>
            <Ionicons
              name="location-outline"
              size={16}
              color={colors.text + '80'}
            />
            <Text style={[styles.infoText, { color: colors.text + '80' }]}>
              {user.location}
            </Text>
          </View>

          <View style={styles.infoRow}>
            <Ionicons
              name="calendar-outline"
              size={16}
              color={colors.text + '80'}
            />
            <Text style={[styles.infoText, { color: colors.text + '80' }]}>
              Member since {user.memberSince}
            </Text>
          </View>

          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, { backgroundColor: colors.primary }]}
              onPress={handleChatPress}
            >
              <Ionicons name="chatbubble-outline" size={18} color="#FFFFFF" />
              <Text style={styles.actionButtonText}>Message</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.actionButton,
                { backgroundColor: colors.card, borderColor: colors.border },
              ]}
              onPress={handleProfilePress}
            >
              <Ionicons name="person-outline" size={18} color={colors.text} />
              <Text style={[styles.actionButtonText, { color: colors.text }]}>
                View Profile
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Shared Items */}
        <View style={styles.sectionContainer}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Shared Items
          </Text>

          {sharedItems.length > 0 ? (
            <FlatList
              data={sharedItems}
              keyExtractor={(item) => item.id}
              horizontal={false}
              scrollEnabled={false}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={[
                    styles.itemCard,
                    {
                      backgroundColor: colors.card,
                      borderColor: colors.border,
                    },
                  ]}
                  onPress={() => handleItemPress(item)}
                >
                  <Image
                    source={{ uri: item.image }}
                    style={styles.itemImage}
                  />
                  <View style={styles.itemInfo}>
                    <Text style={[styles.itemName, { color: colors.text }]}>
                      {item.name}
                    </Text>
                    {item.tradeStatus && (
                      <View
                        style={[
                          styles.statusBadge,
                          {
                            backgroundColor:
                              item.tradeStatus === 'completed'
                                ? '#4CAF5080'
                                : item.tradeStatus === 'pending'
                                ? '#FFC10780'
                                : '#********',
                          },
                        ]}
                      >
                        <Text style={styles.statusText}>
                          {item.tradeStatus.charAt(0).toUpperCase() +
                            item.tradeStatus.slice(1)}
                        </Text>
                      </View>
                    )}
                  </View>
                  <Ionicons
                    name="chevron-forward"
                    size={20}
                    color={colors.text + '60'}
                  />
                </TouchableOpacity>
              )}
            />
          ) : (
            <View style={styles.emptyContainer}>
              <Ionicons
                name="cube-outline"
                size={48}
                color={colors.text + '40'}
              />
              <Text style={[styles.emptyText, { color: colors.text + '80' }]}>
                No shared items yet
              </Text>
            </View>
          )}
        </View>

        {/* Privacy and actions */}
        <View style={[styles.privacySection, { borderColor: colors.border }]}>
          <TouchableOpacity
            style={styles.blockButton}
            onPress={handleBlockUser}
          >
            <Ionicons
              name={
                user.blocked ? 'person-add-outline' : 'person-remove-outline'
              }
              size={20}
              color={user.blocked ? colors.text : '#F44336'}
            />
            <Text
              style={[
                styles.blockButtonText,
                { color: user.blocked ? colors.text : '#F44336' },
              ]}
            >
              {user.blocked ? 'Unblock User' : 'Block User'}
            </Text>
          </TouchableOpacity>

          <Text style={[styles.privacyText, { color: colors.text + '60' }]}>
            Blocking this user will prevent them from messaging you and seeing
            your items.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingTop: 60,
    paddingBottom: 16,
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: '#E0E0E0',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  headerRight: {
    width: 40,
  },
  profileSection: {
    alignItems: 'center',
    padding: 24,
    borderRadius: 12,
    margin: 16,
    marginBottom: 8,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
  },
  name: {
    fontSize: 22,
    fontWeight: '600',
    marginTop: 12,
  },
  username: {
    fontSize: 16,
    marginTop: 4,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  starsContainer: {
    flexDirection: 'row',
    marginRight: 4,
  },
  ratingText: {
    fontSize: 14,
  },
  bio: {
    fontSize: 15,
    lineHeight: 22,
    textAlign: 'center',
    marginTop: 16,
    marginHorizontal: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 12,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 6,
  },
  actionButtons: {
    flexDirection: 'row',
    marginTop: 24,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginHorizontal: 6,
    borderWidth: 1,
    borderColor: 'transparent',
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#FFFFFF',
    marginLeft: 6,
  },
  sectionContainer: {
    margin: 16,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
  },
  itemCard: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: 12,
    padding: 12,
    marginBottom: 10,
    borderWidth: 1,
  },
  itemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  itemInfo: {
    flex: 1,
    marginLeft: 12,
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginTop: 6,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 30,
  },
  emptyText: {
    fontSize: 16,
    marginTop: 12,
  },
  privacySection: {
    margin: 16,
    marginTop: 8,
    paddingTop: 16,
    borderTopWidth: 1,
  },
  blockButton: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  blockButtonText: {
    fontSize: 16,
    marginLeft: 8,
  },
  privacyText: {
    fontSize: 13,
    lineHeight: 18,
  },
});

export default ChatDetailsScreen;
