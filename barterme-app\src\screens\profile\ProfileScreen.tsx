import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  TouchableOpacity,
  ScrollView,
  FlatList,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { NativeStackNavigationProp } from '@react-navigation/native-stack';
import { useTheme } from '../../theme/ThemeProvider';
import { RootStackParamList } from '../../navigation/AppNavigator';

type ProfileScreenNavigationProp =
  NativeStackNavigationProp<RootStackParamList>;

// Placeholder user data
const USER = {
  id: 'user123',
  name: '<PERSON>',
  username: '@johndoe',
  avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
  location: 'New York, NY',
  bio: 'Tech enthusiast and avid collector. Looking to trade gadgets and rare items.',
  rating: 4.8,
  tradedItems: 28,
  memberSince: 'May 2022',
};

// Placeholder listed items data
const LISTED_ITEMS = [
  {
    id: '1',
    title: 'Vintage Polaroid Camera',
    image: 'https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f',
    price: 'Trade Only',
    likes: 24,
  },
  {
    id: '2',
    title: 'Mechanical Keyboard',
    image: 'https://images.unsplash.com/photo-1595225476474-63038da826bf',
    price: 'Trade Only',
    likes: 16,
  },
  {
    id: '3',
    title: 'Sony Headphones',
    image: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e',
    price: 'Trade Only',
    likes: 32,
  },
  {
    id: '4',
    title: 'Vintage Watch',
    image: 'https://images.unsplash.com/photo-1524805444758-089113d48a6d',
    price: 'Trade Only',
    likes: 19,
  },
];

function ProfileScreen() {
  const navigation = useNavigation<ProfileScreenNavigationProp>();
  const { colors } = useTheme();
  const [activeTab, setActiveTab] = useState('items');

  const handleEditProfile = () => {
    navigation.navigate('EditProfile');
  };

  const handleSettings = () => {
    navigation.navigate('Settings');
  };

  const handleItemPress = (itemId: string) => {
    navigation.navigate('ItemDetails', { itemId });
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          onPress={handleSettings}
          style={styles.settingsButton}
        >
          <Ionicons name="settings-outline" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>
          Profile
        </Text>
        <View style={styles.placeholder} />
      </View>

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Profile Info */}
        <View style={styles.profileInfo}>
          <Image source={{ uri: USER.avatar }} style={styles.avatar} />
          <Text style={[styles.name, { color: colors.text }]}>{USER.name}</Text>
          <Text style={[styles.username, { color: colors.text + '99' }]}>
            {USER.username}
          </Text>

          <View style={styles.locationContainer}>
            <Ionicons
              name="location-outline"
              size={16}
              color={colors.primary}
            />
            <Text style={[styles.location, { color: colors.text + 'BB' }]}>
              {USER.location}
            </Text>
          </View>

          <Text style={[styles.bio, { color: colors.text }]}>{USER.bio}</Text>

          <TouchableOpacity
            style={[styles.editButton, { borderColor: colors.primary }]}
            onPress={handleEditProfile}
          >
            <Text style={[styles.editButtonText, { color: colors.primary }]}>
              Edit Profile
            </Text>
          </TouchableOpacity>

          {/* Stats */}
          <View style={styles.statsContainer}>
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {USER.rating}
              </Text>
              <Text style={[styles.statLabel, { color: colors.text + '99' }]}>
                Rating
              </Text>
            </View>
            <View
              style={[styles.divider, { backgroundColor: colors.border }]}
            />
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {USER.tradedItems}
              </Text>
              <Text style={[styles.statLabel, { color: colors.text + '99' }]}>
                Trades
              </Text>
            </View>
            <View
              style={[styles.divider, { backgroundColor: colors.border }]}
            />
            <View style={styles.statItem}>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {USER.memberSince}
              </Text>
              <Text style={[styles.statLabel, { color: colors.text + '99' }]}>
                Member Since
              </Text>
            </View>
          </View>
        </View>

        {/* Tabs */}
        <View style={styles.tabContainer}>
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === 'items' && [
                styles.activeTab,
                { borderBottomColor: colors.primary },
              ],
            ]}
            onPress={() => setActiveTab('items')}
          >
            <Text
              style={[
                styles.tabText,
                {
                  color:
                    activeTab === 'items' ? colors.primary : colors.text + '99',
                },
              ]}
            >
              Listed Items
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[
              styles.tab,
              activeTab === 'trades' && [
                styles.activeTab,
                { borderBottomColor: colors.primary },
              ],
            ]}
            onPress={() => setActiveTab('trades')}
          >
            <Text
              style={[
                styles.tabText,
                {
                  color:
                    activeTab === 'trades'
                      ? colors.primary
                      : colors.text + '99',
                },
              ]}
            >
              Past Trades
            </Text>
          </TouchableOpacity>
        </View>

        {/* Tab Content */}
        {activeTab === 'items' ? (
          <View style={styles.itemsGrid}>
            <FlatList
              data={LISTED_ITEMS}
              keyExtractor={(item) => item.id}
              numColumns={2}
              scrollEnabled={false}
              renderItem={({ item }) => (
                <TouchableOpacity
                  style={styles.itemCard}
                  onPress={() => handleItemPress(item.id)}
                >
                  <Image
                    source={{ uri: item.image }}
                    style={styles.itemImage}
                  />
                  <View style={styles.itemInfo}>
                    <Text
                      style={[styles.itemTitle, { color: colors.text }]}
                      numberOfLines={1}
                    >
                      {item.title}
                    </Text>
                    <View style={styles.itemDetails}>
                      <Text
                        style={[styles.itemPrice, { color: colors.primary }]}
                      >
                        {item.price}
                      </Text>
                      <View style={styles.likesContainer}>
                        <Ionicons
                          name="heart"
                          size={12}
                          color={colors.primary}
                        />
                        <Text
                          style={[
                            styles.likesCount,
                            { color: colors.text + '99' },
                          ]}
                        >
                          {item.likes}
                        </Text>
                      </View>
                    </View>
                  </View>
                </TouchableOpacity>
              )}
            />
          </View>
        ) : (
          <View style={styles.emptyStateContainer}>
            <Ionicons
              name="swap-horizontal"
              size={48}
              color={colors.text + '50'}
            />
            <Text
              style={[styles.emptyStateText, { color: colors.text + '99' }]}
            >
              No past trades to display
            </Text>
          </View>
        )}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 10,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  settingsButton: {
    padding: 4,
  },
  placeholder: {
    width: 24,
  },
  profileInfo: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    marginBottom: 16,
  },
  name: {
    fontSize: 22,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  username: {
    fontSize: 16,
    marginBottom: 12,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  location: {
    fontSize: 14,
    marginLeft: 4,
  },
  bio: {
    fontSize: 14,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
  },
  editButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    marginBottom: 20,
  },
  editButtonText: {
    fontSize: 14,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    paddingVertical: 16,
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
  },
  divider: {
    width: 1,
    height: 30,
  },
  tabContainer: {
    flexDirection: 'row',
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  activeTab: {
    borderBottomWidth: 2,
  },
  tabText: {
    fontSize: 14,
    fontWeight: '600',
  },
  itemsGrid: {
    padding: 12,
  },
  itemCard: {
    flex: 1,
    margin: 6,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  itemImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  itemInfo: {
    padding: 10,
  },
  itemTitle: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
  },
  itemDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  itemPrice: {
    fontSize: 12,
    fontWeight: '600',
  },
  likesContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  likesCount: {
    fontSize: 12,
    marginLeft: 4,
  },
  emptyStateContainer: {
    padding: 40,
    alignItems: 'center',
  },
  emptyStateText: {
    marginTop: 16,
    fontSize: 16,
  },
});

export default ProfileScreen;
