{"name": "ui", "version": "1.0.0", "main": "src/index.ts", "types": "src/index.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx"}, "dependencies": {"nativewind": "^2.0.11", "react": "*", "react-dom": "18.2.0", "react-native": "^0.72.17", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "react-native-svg": "13.9.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.15.0", "@typescript-eslint/parser": "^6.15.0", "eslint": "^8.56.0", "eslint-config-universe": "^12.0.0", "jest": "^29.7.0", "typescript": "^5.1.3"}, "peerDependencies": {"react": "*"}, "eslintConfig": {"extends": "universe/native"}, "private": true}