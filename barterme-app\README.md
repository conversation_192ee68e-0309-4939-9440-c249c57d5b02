# 📱 BarterMe! – Swipe, Trade, Buy. Community-Powered Bartering

BarterMe is a next-gen mobile marketplace inspired by the swipe mechanics of Tinder—but for _items_ instead of people.

Users can **swipe left or right** on items they want to trade with, negotiate in real-time, and even **vote or react** to trades. Whether you're looking to **trade sneakers for speakers** or **buy that rare collectible**, BarterMe is your community-first platform for seamless, social bartering.

## 🚀 Features

- **Swipe-based item discovery** - Find items you love with familiar swipe mechanics
- **Two trading modes** - Public trades with community engagement or private one-to-one trades
- **Real-time messaging** - Negotiate with traders through in-app chat
- **Social reactions** - Comment on and react to public trades
- **Voting system** - Community decides who "won" each trade
- **PayPal integration** - Add cash to sweeten your deal or buy items outright
- **Push notifications** - Stay updated on trade offers, messages, and more

## 🛠️ Tech Stack

| Layer              | Tech                                                                   |
| ------------------ | ---------------------------------------------------------------------- |
| Mobile Frontend    | [Expo](https://expo.dev), React Native, React Reanimated               |
| Backend            | Node.js, Express                                                       |
| Database           | [Supabase](https://supabase.com) (PostgreSQL, Auth, Realtime, Storage) |
| Realtime Chat      | Supabase Realtime Channels                                             |
| Payments           | PayPal SDK                                                             |
| Image Storage      | Supabase Storage                                                       |
| Push Notifications | Expo Notifications                                                     |

## 📋 Prerequisites

- Node.js 18+
- Yarn or npm
- Expo CLI
- Supabase Account
- PayPal Developer Account (for payment features)

## 🏗️ Project Structure

```
barterme-app/
├── apps/
│   └── mobile/         # Expo app
├── packages/
│   ├── backend/        # Node.js API (Express + Supabase)
│   ├── ui/             # Reusable UI components
├── supabase/           # Supabase SQL schema & migrations
├── .env                # API keys (example provided in .env.example)
├── README.md           # This file
└── DEVELOPMENT_PLAN.md # Detailed development roadmap
```

## 🚀 Getting Started

### 1. Clone the repository

```bash
git clone https://github.com/yourusername/barterme-app.git
cd barterme-app
```

### 2. Install dependencies

```bash
# Install root dependencies
yarn install

# Install workspace dependencies
yarn workspaces run install
```

### 3. Set up environment variables

Create a `.env` file in the root directory with your Supabase and PayPal credentials:

```
# Supabase
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_KEY=your_supabase_service_key

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
```

### 4. Start the development server

```bash
# Start the backend server
yarn workspace backend dev

# Start the Expo app
yarn workspace mobile start
```

## 📱 Mobile App

The mobile app is built with Expo and React Native. To run it:

```bash
cd apps/mobile
npx expo start
```

Then, scan the QR code with Expo Go app on your phone, or press 'a' to open in an Android emulator or 'i' for iOS simulator.

## 🖥️ Backend API

The backend API is built with Node.js and Express, using Supabase for data storage and authentication.

```bash
cd packages/backend
yarn dev
```

The API will be available at `http://localhost:3000`.

## 📊 Database

We use Supabase for our database needs. The schema migrations are stored in the `supabase` directory.

## 🧪 Testing

```bash
# Run all tests
yarn test

# Run specific workspace tests
yarn workspace mobile test
yarn workspace backend test
```

## 📦 Building for Production

### Mobile App

```bash
cd apps/mobile
npx expo build:android  # For Android
npx expo build:ios      # For iOS
```

### Backend API

```bash
cd packages/backend
yarn build
```

## 🤝 Contributing

Contributions are welcome! Please see our [CONTRIBUTING.md](CONTRIBUTING.md) for details.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 📧 Contact

For questions or support, please reach out to [<EMAIL>](mailto:<EMAIL>) or open an issue on GitHub.

## 🙏 Acknowledgements

- Inspired by Tinder's swipe mechanics
- Built with love and lots of ☕️
