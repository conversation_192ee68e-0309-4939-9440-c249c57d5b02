import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../context/AuthContext';
import {
  View,
  ActivityIndicator,
  TouchableOpacity,
  Image,
  Text,
} from 'react-native';
import { useTheme } from '../theme/ThemeProvider';
import { useNavigation } from '@react-navigation/native';

// Import Auth Screens
import OnboardingScreen from '../screens/onboarding/OnboardingScreen';
import LoginScreen from '../screens/auth/LoginScreen';
import SignupScreen from '../screens/auth/SignupScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';

// Import Main Screens
import DiscoverScreen from '../screens/discover/DiscoverScreen';
import ItemDetailsScreen from '../screens/main/ItemDetailsScreen';
import SwipeScreen from '../screens/swipe/SwipeScreen';
import TradesScreen from '../screens/trades/TradesScreen';
import TradeOfferScreen from '../screens/trades/TradeOfferScreen';
import TradeDetailsScreen from '../screens/trades/TradeDetailsScreen';
import MessagesScreen from '../screens/messages/MessagesScreen';
import ChatScreen from '../screens/messages/ChatScreen';
import ProfileScreen from '../screens/profile/ProfileScreen';
import NotificationsScreen from '../screens/profile/NotificationsScreen';
import SettingsScreen from '../screens/profile/SettingsScreen';
import SearchScreen from '../screens/discover/SearchScreen';

// Define types for stack navigators
export type RootStackParamList = {
  Onboarding: undefined;
  Auth: undefined;
  MainTabs: undefined;
  ItemDetails: { itemId: string };
  TradeOffer: { itemId?: string; userId?: string };
  TradeDetails: { tradeId: string };
  Chat: { tradeId?: string; userId?: string };
  Profile: { userId?: string };
  Notifications: undefined;
  Settings: undefined;
  Search: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
  Signup: undefined;
  ForgotPassword: undefined;
};

export type MainTabParamList = {
  Discover: undefined;
  Swipe: undefined;
  Trades: undefined;
  Messages: undefined;
  Profile: undefined;
};

const Stack = createNativeStackNavigator<RootStackParamList>();
const AuthStack = createNativeStackNavigator<AuthStackParamList>();
const Tab = createBottomTabNavigator<MainTabParamList>();

// Auth Navigator
const AuthNavigator: React.FC = () => {
  const navigation = useNavigation();
  const { resetOnboarding } = useAuth();

  const handleBackToOnboarding = () => {
    resetOnboarding();
    // This will trigger re-render in AppNavigator which will show the Onboarding screen
  };

  return (
    // @ts-ignore
    <AuthStack.Navigator
      screenOptions={{
        headerShown: true,
        title: '',
        headerLeft: () => (
          <TouchableOpacity
            onPress={handleBackToOnboarding}
            style={{ marginLeft: 10 }}
          >
            <Ionicons name="arrow-back" size={24} color="#000" />
          </TouchableOpacity>
        ),
      }}
    >
      <AuthStack.Screen name="Login" component={LoginScreen} />
      <AuthStack.Screen name="Signup" component={SignupScreen} />
      <AuthStack.Screen
        name="ForgotPassword"
        component={ForgotPasswordScreen}
      />
    </AuthStack.Navigator>
  );
};

// Main Tab Navigator
const MainTabNavigator: React.FC = () => {
  const { colors, isDark } = useTheme();

  // Create a custom button component for the Swipe tab
  const SwipeTabButton = ({ focused }) => (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <View
        style={{
          position: 'absolute',
          bottom: -15,
          height: 68,
          width: 68,
          borderRadius: 34,
          backgroundColor: '#E31937', // Bright red similar to Wendy's
          justifyContent: 'center',
          alignItems: 'center',
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.3,
          shadowRadius: 4.5,
          elevation: 8,
          borderWidth: 3,
          borderColor: '#FFFFFF',
          transform: [{ scale: focused ? 1.05 : 1 }],
        }}
      >
        <Image
          source={require('../../assets/logoIconWhite.png')}
          style={{
            width: 28,
            height: 28,
            marginBottom: 2,
          }}
          resizeMode="contain"
        />
        <Text
          style={{
            color: '#FFFFFF',
            fontSize: 10,
            fontWeight: 'bold',
            marginTop: 2,
          }}
        >
          SWIPE
        </Text>
      </View>
    </View>
  );

  return (
    // @ts-ignore
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof Ionicons.glyphMap = 'help-outline';

          if (route.name === 'Discover') {
            iconName = focused ? 'layers' : 'layers-outline';
          } else if (route.name === 'Trades') {
            iconName = focused ? 'swap-horizontal' : 'swap-horizontal-outline';
          } else if (route.name === 'Messages') {
            iconName = focused ? 'chatbubbles' : 'chatbubbles-outline';
          } else if (route.name === 'Profile') {
            iconName = focused ? 'person' : 'person-outline';
          }

          return <Ionicons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: colors.primary,
        tabBarInactiveTintColor: '#777777',
        headerShown: false,
        tabBarStyle: {
          backgroundColor: colors.tabBarBackground,
          borderTopColor: colors.border,
          height: 65,
          paddingBottom: 5,
        },
      })}
    >
      <Tab.Screen name="Discover" component={DiscoverScreen} />
      <Tab.Screen name="Messages" component={MessagesScreen} />
      <Tab.Screen
        name="Swipe"
        component={SwipeScreen}
        options={{
          tabBarLabel: '',
          tabBarItemStyle: {
            height: 0,
          },
          tabBarIcon: ({ focused }) => <SwipeTabButton focused={focused} />,
        }}
      />
      <Tab.Screen name="Trades" component={TradesScreen} />
      <Tab.Screen name="Profile" component={ProfileScreen} />
    </Tab.Navigator>
  );
};

// Main App Navigator
const AppNavigator: React.FC = () => {
  const { user, loading, onboardingCompleted } = useAuth();
  const { colors } = useTheme();

  if (loading) {
    return (
      <View
        style={{
          flex: 1,
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: colors.background,
        }}
      >
        <ActivityIndicator size="large" color={colors.primary} />
      </View>
    );
  }

  return (
    <NavigationContainer>
      {/* @ts-ignore */}
      <Stack.Navigator screenOptions={{ headerShown: false }}>
        {!user ? (
          // Auth Flow
          <>
            {!onboardingCompleted && (
              <Stack.Screen name="Onboarding" component={OnboardingScreen} />
            )}
            <Stack.Screen name="Auth" component={AuthNavigator} />
          </>
        ) : (
          // Main App Flow
          <>
            <Stack.Screen name="MainTabs" component={MainTabNavigator} />
            <Stack.Screen name="ItemDetails" component={ItemDetailsScreen} />
            <Stack.Screen name="TradeOffer" component={TradeOfferScreen} />
            <Stack.Screen name="TradeDetails" component={TradeDetailsScreen} />
            <Stack.Screen name="Chat" component={ChatScreen} />
            <Stack.Screen name="Profile" component={ProfileScreen} />
            <Stack.Screen
              name="Notifications"
              component={NotificationsScreen}
            />
            <Stack.Screen name="Settings" component={SettingsScreen} />
            <Stack.Screen name="Search" component={SearchScreen} />
          </>
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
